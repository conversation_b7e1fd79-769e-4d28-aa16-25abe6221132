# 即剪AI助手环境变量配置示例

# ===========================================
# AI 后端服务配置
# ===========================================

# AI WebSocket 后端服务地址
VITE_AI_BACKEND_URL=ws://0.0.0.0:8000
VITE_ENABLE_AI_BACKEND=true

# 主要后端服务地址（AI 聊天、指令处理等）
VITE_API_BASE_URL=http://localhost:8000

# WebCodecs 视频处理服务地址
VITE_VIDEO_API_BASE_URL=https://localhost:49306

# 文件上传服务地址
VITE_UPLOAD_API_BASE_URL=http://localhost:8000

# ===========================================
# 用户配置
# ===========================================

VITE_DEFAULT_USER_ID=user_demo
VITE_DEFAULT_PROJECT_ID=1

# ===========================================
# 功能开关
# ===========================================

VITE_ENABLE_INSTRUCTION_VALIDATION=true
VITE_ENABLE_EXECUTION_FEEDBACK=true
VITE_ENABLE_LOCAL_FALLBACK=true

# ===========================================
# WebCodecs 处理配置
# ===========================================

# JPEG 质量 (1-100，数值越大质量越高)
VITE_WEBCODECS_JPEG_QUALITY=80

# 默认目标帧率
VITE_WEBCODECS_TARGET_FPS=5

# 最小帧数限制
VITE_WEBCODECS_MIN_FRAME_COUNT=4

# 最大帧数限制
VITE_WEBCODECS_MAX_FRAME_COUNT=512

# ===========================================
# 调试配置
# ===========================================

VITE_DEBUG_AI_ASSISTANT=false
VITE_LOG_LEVEL=info

# 是否启用 WebCodecs 调试模式
VITE_DEBUG_WEBCODECS=true

# 是否启用性能监控
VITE_PERFORMANCE_MONITORING=true
