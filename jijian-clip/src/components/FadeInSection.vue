<template>
  <div
    ref="sectionRef"
    :class="[
      'fade-in-section transition-all duration-700 ease-out',
      isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
    ]"
    :style="animationStyle"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { addAnimation } from '@/utils/animationManager'

interface Props {
  delay?: number
  threshold?: number
  rootMargin?: string
  once?: boolean
  priority?: 'high' | 'medium' | 'low'
}

const props = withDefaults(defineProps<Props>(), {
  delay: 0,
  threshold: 0.1,
  rootMargin: '50px',
  once: true,
  priority: 'medium'
})

const sectionRef = ref<HTMLElement>()
const isVisible = ref(false)
let animationId: string | null = null

// 🎯 优化：计算属性缓存样式计算
const animationStyle = computed(() => ({
  transitionDelay: `${props.delay}s`,
  willChange: isVisible.value ? 'auto' : 'transform, opacity'
}))

// 🎯 优化：使用单例模式的IntersectionObserver
let globalObserver: IntersectionObserver | null = null
const observedElements = new WeakMap<Element, () => void>()

function getGlobalObserver(threshold: number, rootMargin: string): IntersectionObserver {
  const key = `${threshold}-${rootMargin}`

  if (!globalObserver) {
    globalObserver = new IntersectionObserver(
      (entries) => {
        // 🎯 优化：批处理DOM更新
        const updates: (() => void)[] = []

        entries.forEach((entry) => {
          const callback = observedElements.get(entry.target)
          if (callback && entry.isIntersecting) {
            updates.push(callback)
          }
        })

        // 使用requestAnimationFrame批量执行更新
        if (updates.length > 0) {
          requestAnimationFrame(() => {
            updates.forEach(update => update())
          })
        }
      },
      {
        threshold,
        rootMargin
      }
    )
  }
  return globalObserver
}

let observer: IntersectionObserver | null = null

onMounted(() => {
  if (sectionRef.value) {
    const callback = () => {
      // 🎯 使用动画管理器控制动画
      if (sectionRef.value) {
        animationId = addAnimation({
          element: sectionRef.value,
          animationName: 'opacity-100 translate-y-0',
          duration: 700, // 与CSS transition duration一致
          priority: props.priority,
          callback: () => {
            isVisible.value = true
            animationId = null
          }
        })
      }

      // 如果设置为只触发一次，则移除观察
      if (props.once && sectionRef.value) {
        observer?.unobserve(sectionRef.value)
        observedElements.delete(sectionRef.value)
      }
    }

    observer = getGlobalObserver(props.threshold, props.rootMargin)
    observedElements.set(sectionRef.value, callback)
    observer.observe(sectionRef.value)
  }
})

onUnmounted(() => {
  if (sectionRef.value && observer) {
    observer.unobserve(sectionRef.value)
    observedElements.delete(sectionRef.value)
  }
})
</script>

<style scoped>
/* 🎯 优化的FadeInSection样式 */
.fade-in-section {
  /* GPU加速 */
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* 动画完成后移除will-change */
.fade-in-section.opacity-100 {
  will-change: auto;
}

/* 减少动画在低性能设备上的影响 */
@media (prefers-reduced-motion: reduce) {
  .fade-in-section {
    transition: none !important;
    transform: none !important;
    opacity: 1 !important;
  }
}

/* 在低端设备上简化动画 */
@media (max-width: 768px) {
  .fade-in-section {
    transition-duration: 0.4s; /* 缩短动画时间 */
  }
}
</style>
