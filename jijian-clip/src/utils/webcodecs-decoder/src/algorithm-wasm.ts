/**
 * 视频分段算法模块接口
 */

import { FrameDifference } from './types.js';

export interface SegmentConfig {
  min_avg_duration?: number;
  max_avg_duration?: number;
  initial_pen?: number;
  max_iterations?: number;
  min_size?: number;
  target_fps?: number;
}

export interface SegmentResult {
  segments: number[];
  debug_info: {
    iterations: number;
    final_pen: number;
    avg_durations: number[];
    segment_counts: number[];
  };
  combined_scores: number[];
  success: boolean;
  error?: string;
}

export class AlgorithmProcessor {
  private pyodide: any = null;
  private isInitialized: boolean = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('🔧 正在初始化 Pyodide...');
    const pyodideModule = await (window as any).loadPyodide({
      indexURL: "https://cdn.jsdelivr.net/pyodide/v0.24.1/full/"
    });
    this.pyodide = pyodideModule;

    console.log('📦 正在加载依赖包...');
    // 加载基础包：numpy 和 micropip
    await this.pyodide.loadPackage(['numpy', 'micropip']);

    // 安装 ruptures（通过 micropip）
    console.log('📦 正在安装 ruptures...');
    await this.pyodide.runPythonAsync(`
      import micropip
      await micropip.install('ruptures')
    `);

    console.log('🎯 正在加载算法模块...');
    await this.pyodide.loadPackage('/segment.whl');

    console.log('✅ 算法模块初始化完成');
    this.isInitialized = true;
  }

  async processFeatures(
    frameDifferences: FrameDifference[],
    config: SegmentConfig = {}
  ): Promise<SegmentResult> {
    await this.initialize();

    const features = frameDifferences.map(diff => [
      diff.colorDiff,
      diff.motionDiff,
      diff.textureDiff
    ]);

    // 直接使用秒（我们已经用帧率计算出秒了）
    const timestamps = frameDifferences.map(diff => diff.timestamp);

    console.log('🔍 算法输入调试信息:');
    console.log('特征数量:', features.length);
    console.log('时间戳数量:', timestamps.length);
    console.log('时间戳范围:', timestamps.length > 0 ? `${timestamps[0].toFixed(2)}s - ${timestamps[timestamps.length-1].toFixed(2)}s` : '无');
    console.log('视频总时长:', timestamps.length > 0 ? `${(timestamps[timestamps.length-1] - timestamps[0]).toFixed(2)}s` : '无');

    const algorithmConfig = {
      min_avg_duration: 8,
      max_avg_duration: 50,
      initial_pen: 5,
      max_iterations: 20,
      min_size: 2,
      target_fps: 5,
      ...config
    };

    console.log('算法配置:', algorithmConfig);

    const resultJson = this.pyodide.runPython(`
import algorithm
result = algorithm.process_features_to_segments(
  '${JSON.stringify(features)}',
  '${JSON.stringify(timestamps)}',
  '${JSON.stringify(algorithmConfig)}'
)
result
    `);

    const result: SegmentResult = JSON.parse(resultJson);

    if (!result.success) {
      throw new Error(result.error || '算法处理失败');
    }

    return result;
  }

  isReady(): boolean {
    return this.isInitialized;
  }
}

let algorithmProcessorInstance: AlgorithmProcessor | null = null;

export function getAlgorithmProcessor(): AlgorithmProcessor {
  if (!algorithmProcessorInstance) {
    algorithmProcessorInstance = new AlgorithmProcessor();
  }
  return algorithmProcessorInstance;
}
