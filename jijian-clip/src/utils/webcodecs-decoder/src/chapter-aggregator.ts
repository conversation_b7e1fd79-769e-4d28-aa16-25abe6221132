// 章节聚合器 - 在视频流式上传完成后进行章节聚合

// 章节聚合请求类型
export interface ChapterAggregationRequest {
  video_codes: string[];
}

// 章节聚合响应类型
export interface ChapterAggregationResponse {
  task_id: string;
  status: string; // "success" 或 "failed"
  message: string;
}

// API响应包装类型
export interface APIResponse<T> {
  code: number;
  message: string;
  data?: T;
  error?: {
    type: string;
    details: string;
    field?: string;
    timestamp: string;
  };
  request_id: string;
}

// 章节聚合配置
export interface ChapterAggregatorConfig {
  apiBaseUrl: string; // API基础URL
}

// 章节聚合回调接口
export interface ChapterAggregationCallbacks {
  onProgress?: (status: string) => void; // 进度回调
  onComplete?: (response: ChapterAggregationResponse) => void; // 完成回调
  onError?: (error: Error) => void; // 错误回调
}

/**
 * 章节聚合器
 * 负责在视频流式上传完成后调用后端章节聚合API
 */
export class ChapterAggregator {
  private config: ChapterAggregatorConfig;
  private callbacks: ChapterAggregationCallbacks;

  constructor(config?: Partial<ChapterAggregatorConfig>, callbacks?: ChapterAggregationCallbacks) {
    this.config = {
      // 🎯 使用环境变量配置
      apiBaseUrl: config?.apiBaseUrl ??
        (typeof import.meta !== 'undefined' && import.meta?.env?.VITE_UPLOAD_API_BASE_URL) ??
        'https://localhost:49306',
    };
    this.callbacks = callbacks || {};

    console.log('📚 章节聚合器已创建，配置:', this.config);
  }

  /**
   * 获取默认配置
   */
  static getDefaultConfig(): ChapterAggregatorConfig {
    return {
      // 🎯 使用环境变量配置
      apiBaseUrl: (typeof import.meta !== 'undefined' && import.meta?.env?.VITE_UPLOAD_API_BASE_URL) ??
        'https://localhost:49306',
    };
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<ChapterAggregatorConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('🔧 章节聚合器配置已更新:', this.config);
  }

  /**
   * 更新回调函数
   */
  updateCallbacks(callbacks: ChapterAggregationCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
    console.log('🔧 章节聚合器回调已更新');
  }

  /**
   * 执行章节聚合
   * @param taskId 任务ID
   * @param videoCodes 要聚合的视频代码列表
   */
  async aggregateChapters(taskId: string, videoCodes: string[]): Promise<ChapterAggregationResponse> {
    if (!taskId) {
      throw new Error('任务ID不能为空');
    }

    if (!videoCodes || videoCodes.length === 0) {
      throw new Error('视频代码列表不能为空');
    }

    console.log('📚 开始章节聚合:', { taskId, videoCodes });

    if (this.callbacks.onProgress) {
      this.callbacks.onProgress('开始章节聚合...');
    }

    try {
      const response = await this.callChapterAggregationAPI(taskId, videoCodes);
      
      console.log('✅ 章节聚合完成:', response);
      
      if (this.callbacks.onComplete) {
        this.callbacks.onComplete(response);
      }

      return response;
    } catch (error) {
      console.error('❌ 章节聚合失败:', error);
      
      if (this.callbacks.onError) {
        this.callbacks.onError(error as Error);
      }

      throw error;
    }
  }

  /**
   * 调用章节聚合API（无超时无重试）
   */
  private async callChapterAggregationAPI(taskId: string, videoCodes: string[]): Promise<ChapterAggregationResponse> {
    const request: ChapterAggregationRequest = {
      video_codes: videoCodes
    };

    if (this.callbacks.onProgress) {
      this.callbacks.onProgress('章节聚合中...');
    }

    console.log('📚 章节聚合API调用:', request);

    try {
      const response = await fetch(`${this.config.apiBaseUrl}/api/v1/chapters/aggregate-chapters`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Task-ID': taskId,
        },
        body: JSON.stringify(request),
      });

      const result: APIResponse<ChapterAggregationResponse> = await response.json();

      // 检查HTTP状态码和API响应码
      if (!response.ok) {
        // HTTP错误，尝试从响应中获取错误信息
        const errorMessage = result.error
          ? `${result.message}: ${result.error.details}`
          : result.message || `HTTP ${response.status}`;
        throw new Error(`章节聚合API调用失败: ${errorMessage}`);
      }

      if (result.code !== 200) {
        // API业务错误
        const errorMessage = result.error
          ? `${result.message}: ${result.error.details}`
          : result.message || '未知错误';
        throw new Error(`章节聚合失败: ${errorMessage}`);
      }

      if (!result.data) {
        throw new Error('章节聚合API返回数据为空');
      }

      console.log('✅ 章节聚合API调用成功:', result.data);
      return result.data;

    } catch (error) {
      console.error('❌ 章节聚合API调用失败:', error);
      throw error;
    }
  }

  /**
   * 取消当前操作（如果支持的话）
   */
  cancel(): void {
    console.log('🛑 章节聚合操作已取消');
    // 这里可以添加取消逻辑，比如中断正在进行的请求
  }

  /**
   * 清理资源
   */
  dispose(): void {
    console.log('🧹 章节聚合器资源已清理');
    // 清理任何需要清理的资源
  }
}
