import { YUVFrame, DecoderCallbacks, VideoInfo, DecoderState } from './types.js';
import { checkH265Compatibility, displayH265CompatibilityReport } from '../../h265-compatibility-checker.js';
import { analyzeMP4Structure, displayMP4StructureReport } from '../../mp4-structure-analyzer.js';

// MP4Box 动态导入
let MP4Box: any = null;

// 动态加载 MP4Box
async function loadMP4Box(): Promise<any> {
  if (MP4Box) {
    return MP4Box;
  }

  try {
    console.log('🔄 正在加载 MP4Box 库...');

    // 方式1：尝试从 npm 包导入
    try {
      const mp4boxModule = await import('mp4box');
      MP4Box = mp4boxModule.default || mp4boxModule.MP4Box || mp4boxModule;
      console.log('✅ MP4Box 从 npm 包加载成功');
      return MP4Box;
    } catch (npmError) {
      console.warn('⚠️ npm 包导入失败:', npmError);
    }

    // 方式2：检查全局变量（CDN 加载）
    if ((globalThis as any).MP4Box || (window as any).MP4Box) {
      MP4Box = (globalThis as any).MP4Box || (window as any).MP4Box;
      console.log('✅ MP4Box 从全局变量加载成功');
      return MP4Box;
    }

    // 方式3：动态加载 CDN 版本
    console.log('🌐 尝试从 CDN 加载 MP4Box...');
    await loadMP4BoxFromCDN();

    if ((globalThis as any).MP4Box) {
      MP4Box = (globalThis as any).MP4Box;
      console.log('✅ MP4Box 从 CDN 加载成功');
      return MP4Box;
    }

    throw new Error('所有 MP4Box 加载方式都失败了');

  } catch (error) {
    console.error('❌ MP4Box 加载失败:', error);
    throw new Error(`MP4Box 库加载失败: ${error.message}`);
  }
}

// 从 CDN 加载 MP4Box
function loadMP4BoxFromCDN(): Promise<void> {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    if ((globalThis as any).MP4Box) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://unpkg.com/mp4box@0.5.2/dist/mp4box.all.min.js';
    script.onload = () => resolve();
    script.onerror = () => reject(new Error('CDN 加载失败'));
    document.head.appendChild(script);
  });
}

export class WebCodecsVideoDecoder {
  private decoder: VideoDecoder | null = null;
  private state: DecoderState = 'idle';
  private callbacks: DecoderCallbacks;
  private frameIndex = 0;
  private videoInfo: VideoInfo | null = null;
  private totalSamples = 0;
  private processedSamples = 0;
  private timescale = 1;
  private fps = 0; // 从视频信息计算得出

  // FPS控制相关
  private targetFps: number | null = null;
  private frameSkipInterval = 1; // 每几帧跳过一帧
  private frameCounter = 0; // 当前帧计数器
  private currentFile: File | null = null; // 当前处理的文件

  constructor(callbacks: DecoderCallbacks) {
    this.callbacks = callbacks;
  }

  // 时间戳精度处理函数（与Go版本保持一致，保留3位小数）
  private roundTime(value: number, precision: number = 3): number {
    const multiplier = Math.pow(10, precision);
    return Math.round(value * multiplier) / multiplier;
  }

  // 检查 WebCodecs 支持
  static isSupported(): boolean {
    const hasWebCodecs = typeof VideoDecoder !== 'undefined' &&
                        typeof VideoFrame !== 'undefined';

    // 检查 MP4Box 是否可用（全局变量或已加载）
    const hasMP4Box = !!(
      (globalThis as any).MP4Box ||
      (window as any).MP4Box ||
      MP4Box // 如果已经动态加载
    );

    console.log('🔍 WebCodecs 支持检查:', {
      hasWebCodecs,
      hasMP4Box,
      overall: hasWebCodecs // 暂时只检查 WebCodecs，MP4Box 可以动态加载
    });

    return hasWebCodecs; // MP4Box 可以在需要时动态加载
  }

  // 从视频文件中提取 YUV 数据（参考官方示例）
  private createFrameFromVideoFrame(videoFrame: VideoFrame): YUVFrame {
    // 直接使用VideoFrame对象 - 这是WebCodecs的正确用法
    const width = videoFrame.displayWidth;
    const height = videoFrame.displayHeight;

    // WebCodecs返回的是微秒，转换为秒并保留3位小数（毫秒精度）
    const timestampInSeconds = this.roundTime((videoFrame.timestamp || 0) / 1_000_000);

    console.log(`✅ VideoFrame created: ${width}x${height}, format: ${videoFrame.format}, timestamp: ${timestampInSeconds}`);

    const yuvFrame: YUVFrame = {
      width,
      height,
      timestamp: timestampInSeconds, // 秒单位，3位小数精度
      format: videoFrame.format || 'unknown',
      index: this.frameIndex,
      videoFrame: videoFrame // 直接保留原始VideoFrame对象
    };

    return yuvFrame;
  }

  // 初始化解码器（不配置，等待视频信息）
  async initialize(): Promise<void> {
    if (!WebCodecsVideoDecoder.isSupported()) {
      throw new Error('WebCodecs is not supported in this browser');
    }

    this.state = 'configuring';

    try {
      this.decoder = new VideoDecoder({
        output: (videoFrame: VideoFrame) => {
          try {
            this.frameCounter++;

            // 根据跳帧间隔决定是否输出这一帧
            // 每frameSkipInterval帧取第1帧（从1开始计数）
            if ((this.frameCounter - 1) % this.frameSkipInterval === 0) {
              // 创建帧数据并传递给回调
              // VideoFrame的生命周期转移给特征提取器管理
              const yuvFrame = this.createFrameFromVideoFrame(videoFrame);
              this.callbacks.onFrame(yuvFrame);
              this.frameIndex++;
            } else {
              // 跳过这一帧，立即关闭VideoFrame
              videoFrame.close();
            }
          } catch (error) {
            console.error('Error processing video frame:', error);
            this.callbacks.onError(error as Error);
            // 错误时立即关闭
            videoFrame.close();
          }
        },
        error: (error: Error) => {
          this.state = 'error';
          this.callbacks.onError(error);
        }
      });

      this.state = 'idle';
    } catch (error) {
      this.state = 'error';
      throw error;
    }
  }

  // 解码视频文件
  async decodeVideo(file: File, targetFps?: number): Promise<void> {
    // 保存file引用供后续使用
    this.currentFile = file;
    // 如果解码器未初始化，先初始化
    if (!this.decoder) {
      await this.initialize();
    }

    // 确保 MP4Box 已加载
    const MP4BoxLib = await loadMP4Box();

    this.state = 'decoding';
    this.frameIndex = 0;
    this.frameCounter = 0;
    this.targetFps = targetFps || null;

    try {
      // 使用 MP4Box 解析视频文件
      const arrayBuffer = await file.arrayBuffer();
      const mp4boxFile = MP4BoxLib.createFile();

      // 简化MP4Box配置，使用标准方式

      // 设置视频信息提取
      mp4boxFile.onReady = async (info: any) => {
        const track = info.videoTracks[0];
        if (!track) {
          throw new Error('No video track found');
        }

        // 🎯 使用专业的H.265兼容性检查工具
        const h265CompatibilityResult = await checkH265Compatibility(track.codec);
        const isH265 = h265CompatibilityResult.isH265;

        if (isH265) {
          console.warn('⚠️ 检测到H.265/HEVC格式视频:', track.codec);
          displayH265CompatibilityReport(h265CompatibilityResult);

          // 如果不支持H.265，提前警告但继续尝试
          if (!h265CompatibilityResult.isSupported) {
            console.warn('⚠️ 当前浏览器不支持H.265解码，可能会失败');
            console.warn('💡 建议使用以下命令转换为H.264格式:');
            h265CompatibilityResult.conversionCommands.slice(0, 2).forEach((cmd, index) => {
              console.warn(`   ${index + 1}. ${cmd}`);
            });
          }
        }

        // 关键：使用轨道特定的timescale
        this.timescale = track.timescale;

        // 使用轨道时长计算视频信息
        const durationInSeconds = track.duration / this.timescale;

        this.videoInfo = {
          width: track.track_width || 0,
          height: track.track_height || 0,
          duration: durationInSeconds,
          frameRate: track.nb_samples / durationInSeconds,
          codec: track.codec || 'unknown'
        };

        // 计算帧率：总帧数 / 总时长(秒)
        this.fps = track.nb_samples / durationInSeconds;

        // 记录总样本数
        this.totalSamples = track.nb_samples;
        this.processedSamples = 0;

        // 计算跳帧间隔
        if (this.targetFps && this.videoInfo.frameRate > this.targetFps) {
          this.frameSkipInterval = Math.round(this.videoInfo.frameRate / this.targetFps);
        } else {
          this.frameSkipInterval = 1;
        }

        // 重新配置解码器，包含正确的 codec 和 description
        try {
          const config: VideoDecoderConfig = {
            codec: track.codec,
            hardwareAcceleration: 'prefer-hardware'
          };

          // 使用官方方法提取 description (基于 vjeux 示例)
          let description: Uint8Array | undefined;

          try {
            const fullTrack = mp4boxFile.getTrackById(track.id);
            console.log('Extracting description for codec:', track.codec);

            // 从 stsd entries 中获取 avcC/hvcC 并使用 DataStream 正确提取
            if (fullTrack?.mdia?.minf?.stbl?.stsd?.entries) {
              for (const entry of fullTrack.mdia.minf.stbl.stsd.entries) {
                if (entry.avcC || entry.hvcC) {
                  console.log('🔍 找到编码配置:', {
                    hasAvcC: !!entry.avcC,
                    hasHvcC: !!entry.hvcC,
                    codec: track.codec
                  });

                  // 尝试多种方法提取 description
                  try {
                    // 方法1：使用 MP4Box 的 DataStream
                    const DataStream = (window as any).DataStream || (globalThis as any).DataStream;

                    if (DataStream) {
                      console.log('✅ 使用 DataStream 提取 description');
                      const stream = new DataStream(undefined, 0, DataStream.BIG_ENDIAN);

                      if (entry.avcC && track.codec.startsWith('avc1')) {
                        entry.avcC.write(stream);
                        description = new Uint8Array(stream.buffer, 8); // Remove the box header
                        console.log('✅ Extracted H.264 avcC description, length:', description.length);
                        break;
                      } else if (entry.hvcC && (track.codec.startsWith('hev1') || track.codec.startsWith('hvc1'))) {
                        entry.hvcC.write(stream);
                        description = new Uint8Array(stream.buffer, 8); // Remove the box header
                        console.log('✅ Extracted H.265 hvcC description, length:', description.length);
                        break;
                      }
                    } else {
                      // 方法2：直接从 entry 数据提取（备用方案）
                      console.log('⚠️ DataStream 不可用，尝试直接提取');

                      if (entry.avcC && track.codec.startsWith('avc1')) {
                        // 尝试直接访问 avcC 数据
                        if (entry.avcC.data) {
                          description = new Uint8Array(entry.avcC.data);
                          console.log('✅ 直接提取 H.264 avcC description, length:', description.length);
                          break;
                        }
                      } else if (entry.hvcC && (track.codec.startsWith('hev1') || track.codec.startsWith('hvc1'))) {
                        // 🎯 增强 H.265 hvcC 数据提取
                        console.log('🔍 尝试提取 H.265 hvcC 数据:', entry.hvcC);

                        if (entry.hvcC.data) {
                          description = new Uint8Array(entry.hvcC.data);
                          console.log('✅ 直接提取 H.265 hvcC description, length:', description.length);
                          break;
                        } else if (entry.hvcC.buffer) {
                          // 尝试从 buffer 提取
                          description = new Uint8Array(entry.hvcC.buffer);
                          console.log('✅ 从 buffer 提取 H.265 hvcC description, length:', description.length);
                          break;
                        } else if (entry.hvcC.write) {
                          // 尝试使用 write 方法序列化
                          try {
                            const stream = new (window as any).DataStream(undefined, 0, (window as any).DataStream.BIG_ENDIAN);
                            entry.hvcC.write(stream);
                            description = new Uint8Array(stream.buffer, 8); // 跳过头部8字节
                            console.log('✅ 通过序列化提取 H.265 hvcC description, length:', description.length);
                            break;
                          } catch (writeError) {
                            console.warn('⚠️ hvcC 序列化失败:', writeError);
                          }
                        }
                      }
                    }
                  } catch (extractError) {
                    console.error('❌ Description 提取失败:', extractError);
                  }
                }
              }
            }
          } catch (error) {
            console.error('Error extracting description:', error);
          }

          if (!description) {
            console.error('❌ No description found for codec:', track.codec);

            // 根据编码格式提供更准确的错误信息
            let errorMessage = `No description found for codec: ${track.codec}.`;
            if (track.codec.startsWith('avc1')) {
              errorMessage += ' H.264 videos require avcC description.';
            } else if (track.codec.startsWith('hvc1') || track.codec.startsWith('hev1')) {
              errorMessage += ' H.265/HEVC videos require hvcC description.';
            } else {
              errorMessage += ' This codec may not be supported by WebCodecs.';
            }

            throw new Error(errorMessage);
          }

          // 添加 description 到配置
          if (description) {
            config.description = description;
            console.log('✅ Added description to config, length:', description.length);
          }

          // 检查浏览器是否支持该编码格式
          console.log('🔍 检查编码格式支持:', track.codec);

          try {
            const isSupported = await VideoDecoder.isConfigSupported(config);
            if (!isSupported.supported) {
              console.error('❌ 浏览器不支持该编码格式:', track.codec);

              // 🎯 为H.265格式提供更详细的错误信息
              if (isH265) {
                throw new Error(`H.265/HEVC编码格式 (${track.codec}) 在当前浏览器中不受支持。\n\n原因：\n- 大多数浏览器的WebCodecs API不支持H.265解码\n- H.265需要额外的许可证费用，浏览器厂商通常不内置支持\n\n解决方案：\n1. 转换为H.264格式：ffmpeg -i input.mp4 -c:v libx264 -preset fast -crf 23 output.mp4\n2. 使用在线转换工具将视频重新编码为H.264格式`);
              } else {
                throw new Error(`浏览器不支持编码格式: ${track.codec}. 请尝试使用 H.264 编码的视频。`);
              }
            }
            console.log('✅ 编码格式支持检查通过:', track.codec);
          } catch (supportError) {
            console.error('❌ 编码格式支持检查失败:', supportError);

            // 🎯 如果是H.265格式，提供更友好的错误信息
            if (isH265 && supportError.message.includes('不受支持')) {
              throw supportError; // 直接抛出已经格式化的H.265错误信息
            } else {
              throw new Error(`编码格式 ${track.codec} 不受支持: ${supportError.message}`);
            }
          }

          console.log('Configuring decoder with:', config);
          this.decoder!.configure(config);

          // 记录总样本数
          console.log('🎬 开始提取样本，总样本数:', track.nb_samples);

          // 🎯 验证track的完整性，防止findPosition错误
          try {
            console.log('🔍 开始Track完整性验证...');
            console.log('🔍 Track基本信息:', {
              codec: track.codec,
              nb_samples: track.nb_samples,
              duration: track.duration,
              timescale: track.timescale,
              hasStsc: !!track.stsc,
              hasStco: !!track.stco,
              hasCo64: !!track.co64,
              hasSamples: !!track.samples,
              samplesLength: track.samples ? track.samples.length : 0,
              samplesType: typeof track.samples
            });

            // 🎯 详细检查samples数据结构
            if (track.samples) {
              console.log('🔍 Samples详细信息:', {
                length: track.samples.length,
                isArray: Array.isArray(track.samples),
                firstSample: track.samples.length > 0 ? {
                  hasData: !!track.samples[0]?.data,
                  dataType: typeof track.samples[0]?.data,
                  dataLength: track.samples[0]?.data?.byteLength,
                  hasCts: !!track.samples[0]?.cts,
                  hasIsSync: track.samples[0]?.is_sync !== undefined
                } : null
              });
            }

            // 🎯 检查MP4Box是否正确解析了track结构
            // 有时MP4Box需要更多时间来解析track的详细信息
            if (!track.samples || track.samples.length === 0) {
              console.warn('⚠️ Track samples 为空，尝试重新获取track信息...');

              // 尝试从MP4Box文件中重新获取track信息
              const fullTrack = mp4boxFile.getTrackById(track.id);
              if (fullTrack && fullTrack.samples && fullTrack.samples.length > 0) {
                console.log('✅ 从完整track中获取到samples:', fullTrack.samples.length);
                track.samples = fullTrack.samples;
                track.stsc = fullTrack.stsc || track.stsc;
                track.stco = fullTrack.stco || track.stco;
                track.co64 = fullTrack.co64 || track.co64;
              } else {
                // 🎯 使用MP4结构分析工具进行详细诊断
                console.error('❌ 无法从MP4文件中解析track数据结构，开始详细分析...');

                try {
                  const structureInfo = await analyzeMP4Structure(this.currentFile!);
                  displayMP4StructureReport(structureInfo);

                  // 根据分析结果生成更准确的错误信息
                  let errorMessage = 'MP4文件的track数据结构不完整';
                  if (structureInfo.issues.length > 0) {
                    errorMessage += `：${structureInfo.issues.join('; ')}`;
                  }

                  if (structureInfo.recommendations.length > 0) {
                    errorMessage += `\n\n建议解决方案：\n${structureInfo.recommendations.join('\n')}`;
                  }

                  if (isH265) {
                    errorMessage = 'H.265/HEVC格式视频的track数据结构不完整。H.265格式在当前WebCodecs实现中存在兼容性问题，建议转换为H.264格式。\n\n' + errorMessage;
                  }

                  throw new Error(errorMessage);
                } catch (analysisError) {
                  console.error('❌ MP4结构分析也失败了:', analysisError);

                  if (isH265) {
                    throw new Error('H.265/HEVC格式视频的track数据结构不完整。H.265格式在当前WebCodecs实现中存在兼容性问题，建议转换为H.264格式。');
                  } else {
                    throw new Error('MP4文件的track数据结构不完整，可能是文件损坏或编码器兼容性问题。建议重新编码视频文件。');
                  }
                }
              }
            }

            // 检查第一个sample是否有效
            const firstSample = track.samples[0];
            if (!firstSample || typeof firstSample.offset === 'undefined') {
              console.warn('⚠️ 第一个sample数据无效');
              console.log('🔍 第一个sample结构:', firstSample);
              throw new Error('视频sample数据结构不完整');
            }

            // 检查track的stsc表（sample-to-chunk表）
            if (!track.stsc || !track.stsc.entries || track.stsc.entries.length === 0) {
              console.warn('⚠️ Track stsc表缺失，尝试重建或绕过...');
              console.log('🔍 stsc表状态:', {
                hasStsc: !!track.stsc,
                hasEntries: !!(track.stsc && track.stsc.entries),
                entriesLength: track.stsc && track.stsc.entries ? track.stsc.entries.length : 0
              });

              // 🎯 尝试从完整track获取stsc信息
              try {
                const fullTrack = mp4boxFile.getTrackById(track.id);
                if (fullTrack && fullTrack.stsc && fullTrack.stsc.entries && fullTrack.stsc.entries.length > 0) {
                  console.log('✅ 从完整track中获取到stsc表:', fullTrack.stsc.entries.length);
                  track.stsc = fullTrack.stsc;
                } else {
                  // 🎯 尝试使用MP4Box的内部方法重建索引
                  console.warn('⚠️ 尝试重建stsc表...');

                  // 检查是否可以通过其他方式获取chunk信息
                  if (track.samples && track.samples.length > 0) {
                    console.log('🔧 检测到samples元数据，但需要检查是否有实际数据...');

                    // 🎯 检查samples是否包含实际数据
                    const firstSample = track.samples[0];
                    if (firstSample && firstSample.data && firstSample.data.byteLength > 0) {
                      console.log('✅ Samples包含实际数据，直接使用');
                      this.totalSamples = track.samples.length;
                      this.handleDirectSampleExtraction(track, mp4boxFile);
                      return;
                    } else {
                      console.warn('⚠️ Samples只有元数据，没有实际数据，尝试强制提取...');

                      // 🎯 尝试强制MP4Box提取sample数据，即使stsc表缺失
                      try {
                        console.log('🔧 尝试强制设置提取选项...');
                        mp4boxFile.setExtractionOptions(track.id, null, {
                          nbSamples: 1 // 先尝试提取1个样本测试
                        });

                        // 设置一个临时的onSamples回调来测试
                        const originalOnSamples = mp4boxFile.onSamples;
                        mp4boxFile.onSamples = (id: number, user: any, samples: any[]) => {
                          console.log('🎯 强制提取测试成功，获得samples:', samples.length);
                          if (samples.length > 0 && samples[0].data) {
                            console.log('✅ 强制提取成功，切换到正常流程');
                            // 恢复原始回调
                            mp4boxFile.onSamples = originalOnSamples;
                            // 继续正常的MP4Box流程
                            mp4boxFile.setExtractionOptions(track.id, null, {
                              nbSamples: 100
                            });
                          } else {
                            console.error('❌ 强制提取也失败');
                            throw new Error('无法提取sample数据');
                          }
                        };

                        // 尝试启动MP4Box
                        mp4boxFile.start();
                        console.log('✅ 强制提取启动成功');
                        return; // 成功则返回，继续正常流程

                      } catch (forceError) {
                        console.error('❌ 强制提取失败:', forceError);
                        // 继续到错误处理
                      }
                    }
                  } else {
                    // 如果连samples都没有，才抛出错误
                    if (isH265) {
                      throw new Error('H.265/HEVC格式视频缺少必要的索引信息(stsc表)。这是H.265格式在MP4Box中的已知兼容性问题，建议使用H.264格式。');
                    } else {
                      throw new Error('视频文件缺少必要的索引信息(stsc表)，无法进行帧定位');
                    }
                  }
                }
              } catch (stscError) {
                console.error('❌ stsc表处理失败:', stscError);
                if (isH265) {
                  throw new Error('H.265/HEVC格式视频缺少必要的索引信息(stsc表)。这是H.265格式在MP4Box中的已知兼容性问题，建议使用H.264格式。');
                } else {
                  throw new Error('视频文件缺少必要的索引信息(stsc表)，无法进行帧定位');
                }
              }
            }

            // 检查track的stco表（chunk offset表）
            if (!track.stco && !track.co64) {
              console.warn('⚠️ Track chunk offset表缺失');
              console.log('🔍 chunk offset表状态:', {
                hasStco: !!track.stco,
                hasCo64: !!track.co64
              });

              // 🎯 如果有samples数据，可以绕过chunk offset表
              if (track.samples && track.samples.length > 0) {
                console.log('✅ 有samples数据，可以绕过chunk offset表检查');
                // 继续处理，不抛出错误
              } else {
                if (isH265) {
                  throw new Error('H.265/HEVC格式视频缺少chunk偏移信息。这可能是H.265编码器生成的MP4文件结构问题，建议重新编码为H.264格式。');
                } else {
                  throw new Error('视频文件缺少chunk偏移信息，无法定位视频数据');
                }
              }
            }

            console.log('✅ Track完整性验证通过');
          } catch (validationError) {
            console.error('❌ Track验证失败:', validationError);

            // 🎯 根据是否为H.265格式提供不同的错误信息
            let errorMessage = `视频文件格式不兼容: ${validationError.message}`;
            if (isH265) {
              errorMessage += '\n\n💡 解决建议：\n1. 使用FFmpeg转换为H.264格式：ffmpeg -i input.mp4 -c:v libx264 -c:a copy output.mp4\n2. 或使用其他视频转换工具重新编码为H.264格式';
            } else {
              errorMessage += '。建议使用H.264格式的MP4文件。';
            }

            this.callbacks.onError(new Error(errorMessage));
            return;
          }

          // 🎯 根据stsc表状态调整提取策略
          if (track.stsc && track.stsc.entries && track.stsc.entries.length > 0) {
            // 正常情况：有完整的stsc表
            mp4boxFile.setExtractionOptions(track.id, null, {
              nbSamples: 100 // 批量提取
            });
          } else {
            // 特殊情况：没有stsc表，尝试更保守的提取
            console.warn('⚠️ 使用保守的样本提取策略（无stsc表）');
            mp4boxFile.setExtractionOptions(track.id, null, {
              nbSamples: 10 // 更小的批次
            });
          }

          // 🎯 安全地启动 MP4Box，捕获内部错误
          try {
            console.log('🎬 启动MP4Box样本提取...');
            mp4boxFile.start();
            console.log('✅ MP4Box启动成功');
          } catch (startError) {
            console.error('❌ MP4Box start 失败:', startError);

            // 🎯 如果是索引相关错误，尝试替代方案
            if (startError.message && (startError.message.includes('findPosition') ||
                                     startError.message.includes('Cannot read properties of undefined') ||
                                     startError.message.includes('stsc') ||
                                     startError.message.includes('chunk'))) {

              console.warn('⚠️ 标准提取失败，尝试直接样本访问...');

              // 尝试直接访问samples而不依赖MP4Box的内部索引
              try {
                this.handleDirectSampleExtraction(track, mp4boxFile);
                return; // 成功则返回
              } catch (directError) {
                console.error('❌ 直接样本提取也失败:', directError);
                this.callbacks.onError(new Error('视频文件格式不兼容，MP4Box 无法解析此视频文件的索引结构。请尝试使用H.264格式的MP4文件，或使用其他视频转换工具重新编码。'));
              }
            } else {
              this.callbacks.onError(startError as Error);
            }
            return;
          }
        } catch (error) {
          this.callbacks.onError(error as Error);
        }
      };

      // 处理提取的样本 - 修复完成逻辑
      // 在decodeVideo方法中，修改onSamples回调函数
      mp4boxFile.onSamples = (_id: number, _user: any, samples: any[]) => {
        for (const sample of samples) {
          if (this.decoder && this.decoder.state === 'configured') {
            try {

              const pts = sample.cts;
              
              // 直接转换为微秒（避免精度损失）
              const timestampInMicroseconds = Math.round(
                (pts / this.timescale) * 1_000_000
              );

              const chunk = new EncodedVideoChunk({
                type: sample.is_sync ? 'key' : 'delta',
                timestamp: timestampInMicroseconds, // 使用计算后的微秒时间戳
                data: sample.data
              });

              this.decoder.decode(chunk);
              this.processedSamples++;

              // 检查是否处理完所有样本
              if (this.processedSamples >= this.totalSamples) {
                this.decoder.flush().then(() => {
                  this.state = 'completed';
                  this.callbacks.onComplete();
                }).catch(error => {
                  this.callbacks.onError(error);
                });
              }
            } catch (error) {
              this.callbacks.onError(error as Error);
            }
          }
        }
      };

      // MP4Box flush完成回调 - 作为备用机制
      mp4boxFile.onFlush = () => {
        console.log('🎬 MP4Box onFlush called - 备用完成机制');
        // 如果还没有完成，则作为备用机制
        if (this.state !== 'completed' && this.decoder) {
          console.log('🎬 使用备用完成机制...');
          this.decoder.flush().then(() => {
            this.state = 'completed';
            console.log('✅ 备用机制：解码完成，调用onComplete');
            this.callbacks.onComplete();
          }).catch(error => {
            console.error('❌ 备用机制：解码器flush错误:', error);
            this.callbacks.onError(error);
          });
        }
      };

      // 🎯 增强错误处理和调试信息
      mp4boxFile.onError = (error: any) => {
        console.error('❌ MP4Box error:', error);
        console.error('❌ MP4Box错误详情:', {
          error,
          fileSize: arrayBuffer.byteLength,
          hasVideoTracks: info?.videoTracks?.length || 0,
          hasAudioTracks: info?.audioTracks?.length || 0
        });
        this.callbacks.onError(new Error(`MP4Box解析错误: ${error}。可能是文件格式不兼容或文件损坏。`));
      };

      // 移除可能干扰的调试回调，使用最简配置

      // 🎯 使用正确的MP4文件解析方式（一次性追加完整数据）
      console.log('🎬 开始解析MP4文件，大小:', arrayBuffer.byteLength);

      // 🔧 修复：一次性追加完整数据，不要分块
      // 分块追加会破坏MP4文件结构，导致索引表不完整
      (arrayBuffer as any).fileStart = 0;
      mp4boxFile.appendBuffer(arrayBuffer);
      mp4boxFile.flush();

    } catch (error) {
      this.state = 'error';
      this.callbacks.onError(error as Error);
    }
  }

  // 获取视频信息
  getVideoInfo(): VideoInfo | null {
    return this.videoInfo;
  }

  // 获取当前状态
  getState(): DecoderState {
    return this.state;
  }

  // 🎯 直接样本提取方法（绕过MP4Box的索引系统）
  private handleDirectSampleExtraction(track: any, mp4boxFile: any): void {
    console.log('🔧 尝试直接样本提取，绕过MP4Box索引系统...');

    if (!track.samples || track.samples.length === 0) {
      throw new Error('无法进行直接样本提取：samples数据不可用');
    }

    // 记录总样本数
    this.totalSamples = track.samples.length;
    console.log('🎬 直接提取模式，总样本数:', this.totalSamples);

    // 模拟onSamples回调，直接处理samples
    const batchSize = 10; // 小批次处理
    let processedCount = 0;

    const processBatch = () => {
      const endIndex = Math.min(processedCount + batchSize, track.samples.length);
      const batch = track.samples.slice(processedCount, endIndex);

      console.log(`🔄 处理样本批次: ${processedCount}-${endIndex}`);

      // 调用现有的样本处理逻辑
      for (const sample of batch) {
        if (this.decoder && this.decoder.state === 'configured') {
          try {
            // 🎯 详细检查sample数据结构
            console.log('🔍 Sample数据结构:', {
              hasData: !!sample.data,
              dataType: typeof sample.data,
              dataLength: sample.data?.byteLength,
              hasCts: sample.cts !== undefined,
              cts: sample.cts,
              hasIsSync: sample.is_sync !== undefined,
              isSync: sample.is_sync,
              sampleKeys: Object.keys(sample)
            });

            // 检查必要的数据是否存在
            if (!sample.data) {
              console.error('❌ Sample缺少data属性，跳过此sample');
              continue;
            }

            if (sample.cts === undefined) {
              console.error('❌ Sample缺少cts时间戳，跳过此sample');
              continue;
            }

            const pts = sample.cts;
            const timestampInMicroseconds = Math.round((pts / this.timescale) * 1_000_000);

            const chunk = new EncodedVideoChunk({
              type: sample.is_sync ? 'key' : 'delta',
              timestamp: timestampInMicroseconds,
              data: sample.data
            });

            this.decoder.decode(chunk);
            this.processedSamples++;
          } catch (error) {
            console.error('❌ 直接样本处理错误:', error);
            console.error('❌ 错误的sample:', sample);
            throw error;
          }
        }
      }

      processedCount = endIndex;

      // 检查是否完成
      if (processedCount >= track.samples.length) {
        console.log('✅ 直接样本提取完成');
        this.decoder!.flush().then(() => {
          this.state = 'completed';
          this.callbacks.onComplete();
        }).catch(error => {
          this.callbacks.onError(error);
        });
      } else {
        // 继续处理下一批
        setTimeout(processBatch, 10); // 小延迟避免阻塞
      }
    };

    // 开始处理
    processBatch();
  }

  // 清理资源
  dispose(): void {
    if (this.decoder) {
      this.decoder.close();
      this.decoder = null;
    }
    this.state = 'idle';
    this.frameIndex = 0;
    this.videoInfo = null;
    this.currentFile = null;
  }
}
