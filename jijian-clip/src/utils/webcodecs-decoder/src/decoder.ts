import { YUVFrame, DecoderCallbacks, VideoInfo, DecoderState } from './types.js';

// 动态加载 MP4Box 库
let MP4Box: any = null;

async function loadMP4Box(): Promise<any> {
  if (MP4Box) {
    return MP4Box;
  }

  try {
    console.log('🔄 正在加载 MP4Box 库...');
    const mp4boxModule = await import('mp4box');
    MP4Box = mp4boxModule.default || mp4boxModule.MP4Box || mp4boxModule;
    console.log('✅ MP4Box 库加载成功');
    return MP4Box;
  } catch (error) {
    console.error('❌ MP4Box 库加载失败:', error);
    throw new Error(`MP4Box加载失败: ${error.message}`);
  }
}

export class WebCodecsVideoDecoder {
  private decoder: VideoDecoder | null = null;
  private state: DecoderState = 'idle';
  private callbacks: DecoderCallbacks;
  private frameIndex = 0;
  private videoInfo: VideoInfo | null = null;
  private totalSamples = 0;
  private processedSamples = 0;
  private timescale = 1;
  private fps = 0; // 从视频信息计算得出

  // FPS控制相关
  private targetFps: number | null = null;
  private frameSkipInterval = 1; // 每几帧跳过一帧
  private frameCounter = 0; // 当前帧计数器

  constructor(callbacks: DecoderCallbacks) {
    this.callbacks = callbacks;
  }

  // 时间戳精度处理函数（与Go版本保持一致，保留3位小数）
  private roundTime(value: number, precision: number = 3): number {
    const multiplier = Math.pow(10, precision);
    return Math.round(value * multiplier) / multiplier;
  }

  // 检查 WebCodecs 支持
  static isSupported(): boolean {
    return typeof VideoDecoder !== 'undefined' && 
           typeof VideoFrame !== 'undefined';
  }

  // 从视频文件中提取 YUV 数据（参考官方示例）
  private createFrameFromVideoFrame(videoFrame: VideoFrame): YUVFrame {
    // 直接使用VideoFrame对象 - 这是WebCodecs的正确用法
    const width = videoFrame.displayWidth;
    const height = videoFrame.displayHeight;

    // WebCodecs返回的是微秒，转换为秒并保留3位小数（毫秒精度）
    const timestampInSeconds = this.roundTime((videoFrame.timestamp || 0) / 1_000_000);

    console.log(`✅ VideoFrame created: ${width}x${height}, format: ${videoFrame.format}, timestamp: ${timestampInSeconds}`);

    const yuvFrame: YUVFrame = {
      width,
      height,
      timestamp: timestampInSeconds, // 秒单位，3位小数精度
      format: videoFrame.format || 'unknown',
      index: this.frameIndex,
      videoFrame: videoFrame // 直接保留原始VideoFrame对象
    };

    return yuvFrame;
  }

  // 初始化解码器（不配置，等待视频信息）
  async initialize(): Promise<void> {
    if (!WebCodecsVideoDecoder.isSupported()) {
      throw new Error('WebCodecs is not supported in this browser');
    }

    this.state = 'configuring';

    try {
      this.decoder = new VideoDecoder({
        output: (videoFrame: VideoFrame) => {
          try {
            this.frameCounter++;

            // 根据跳帧间隔决定是否输出这一帧
            // 每frameSkipInterval帧取第1帧（从1开始计数）
            if ((this.frameCounter - 1) % this.frameSkipInterval === 0) {
              // 创建帧数据并传递给回调
              // VideoFrame的生命周期转移给特征提取器管理
              const yuvFrame = this.createFrameFromVideoFrame(videoFrame);
              this.callbacks.onFrame(yuvFrame);
              this.frameIndex++;
            } else {
              // 跳过这一帧，立即关闭VideoFrame
              videoFrame.close();
            }
          } catch (error) {
            console.error('Error processing video frame:', error);
            this.callbacks.onError(error as Error);
            // 错误时立即关闭
            videoFrame.close();
          }
        },
        error: (error: Error) => {
          this.state = 'error';
          this.callbacks.onError(error);
        }
      });

      this.state = 'idle';
    } catch (error) {
      this.state = 'error';
      throw error;
    }
  }

  // 解码视频文件
  async decodeVideo(file: File, targetFps?: number): Promise<void> {
    // 如果解码器未初始化，先初始化
    if (!this.decoder) {
      await this.initialize();
    }

    this.state = 'decoding';
    this.frameIndex = 0;
    this.frameCounter = 0;
    this.targetFps = targetFps || null;

    try {
      // 动态加载 MP4Box
      const MP4BoxLib = await loadMP4Box();

      // 使用 MP4Box 解析视频文件
      const arrayBuffer = await file.arrayBuffer();
      const mp4boxFile = MP4BoxLib.createFile();

      // 设置视频信息提取
      mp4boxFile.onReady = async (info: any) => {
        const track = info.videoTracks[0];
        if (!track) {
          throw new Error('No video track found');
        }

        // 关键：使用轨道特定的timescale
        this.timescale = track.timescale;

        // 使用轨道时长计算视频信息
        const durationInSeconds = track.duration / this.timescale;

        this.videoInfo = {
          width: track.track_width || 0,
          height: track.track_height || 0,
          duration: durationInSeconds,
          frameRate: track.nb_samples / durationInSeconds,
          codec: track.codec || 'unknown'
        };

        // 计算帧率：总帧数 / 总时长(秒)
        this.fps = track.nb_samples / durationInSeconds;

        // 记录总样本数
        this.totalSamples = track.nb_samples;
        this.processedSamples = 0;

        // 计算跳帧间隔
        if (this.targetFps && this.videoInfo.frameRate > this.targetFps) {
          this.frameSkipInterval = Math.round(this.videoInfo.frameRate / this.targetFps);
        } else {
          this.frameSkipInterval = 1;
        }

        // 重新配置解码器，包含正确的 codec 和 description
        try {
          const config: VideoDecoderConfig = {
            codec: track.codec,
            hardwareAcceleration: 'prefer-hardware'
          };

          // 使用官方方法提取 description (基于 vjeux 示例)
          let description: Uint8Array | undefined;

          try {
            const fullTrack = mp4boxFile.getTrackById(track.id);
            console.log('Extracting description for codec:', track.codec);

            // 从 stsd entries 中获取 avcC/hvcC 并使用 DataStream 正确提取
            if (fullTrack?.mdia?.minf?.stbl?.stsd?.entries) {
              for (const entry of fullTrack.mdia.minf.stbl.stsd.entries) {
                if (entry.avcC || entry.hvcC) {
                  // 尝试使用 MP4Box 的 DataStream 来正确提取 description
                  const DataStream = (window as any).DataStream || (globalThis as any).DataStream || MP4BoxLib.DataStream;

                  if (DataStream) {
                    try {
                      const stream = new DataStream(undefined, 0, DataStream.BIG_ENDIAN);
                      if (entry.avcC && track.codec.startsWith('avc1')) {
                        entry.avcC.write(stream);
                        description = new Uint8Array(stream.buffer, 8); // Remove the box header
                        console.log('✅ Extracted H.264 avcC description, length:', description.length);
                        break;
                      } else if (entry.hvcC && (track.codec.startsWith('hev1') || track.codec.startsWith('hvc1'))) {
                        entry.hvcC.write(stream);
                        description = new Uint8Array(stream.buffer, 8); // Remove the box header
                        console.log('✅ Extracted H.265 hvcC description, length:', description.length);
                        break;
                      }
                    } catch (dataStreamError) {
                      console.warn('⚠️ DataStream提取失败:', dataStreamError);
                    }
                  } else {
                    console.warn('⚠️ DataStream不可用，尝试替代方案...');

                    // 替代方案：直接从entry中提取description
                    if (entry.avcC && track.codec.startsWith('avc1')) {
                      // 对于H.264，尝试直接使用avcC的data
                      if (entry.avcC.data && entry.avcC.data.length > 0) {
                        description = new Uint8Array(entry.avcC.data);
                        console.log('✅ 使用替代方案提取H.264 description, length:', description.length);
                        break;
                      }
                    }
                  }
                }
              }
            }
          } catch (error) {
            console.error('Error extracting description:', error);
          }

          if (!description) {
            console.error('❌ No description found for codec:', track.codec);
            throw new Error(`No description found for codec: ${track.codec}. H.264 videos require avcC description.`);
          }

          // 添加 description 到配置
          if (description) {
            config.description = description;
            console.log('✅ Added description to config, length:', description.length);
          }

          console.log('Configuring decoder with:', config);
          this.decoder!.configure(config);

          // 记录总样本数
          console.log('🎬 开始提取样本，总样本数:', track.nb_samples);

          mp4boxFile.setExtractionOptions(track.id, null, {
            nbSamples: 100 // 批量提取
          });
          mp4boxFile.start();
        } catch (error) {
          this.callbacks.onError(error as Error);
        }
      };

      // 处理提取的样本 - 修复完成逻辑
      // 在decodeVideo方法中，修改onSamples回调函数
      mp4boxFile.onSamples = (_id: number, _user: any, samples: any[]) => {
        for (const sample of samples) {
          if (this.decoder && this.decoder.state === 'configured') {
            try {

              const pts = sample.cts;
              
              // 直接转换为微秒（避免精度损失）
              const timestampInMicroseconds = Math.round(
                (pts / this.timescale) * 1_000_000
              );

              const chunk = new EncodedVideoChunk({
                type: sample.is_sync ? 'key' : 'delta',
                timestamp: timestampInMicroseconds, // 使用计算后的微秒时间戳
                data: sample.data
              });

              this.decoder.decode(chunk);
              this.processedSamples++;

              // 检查是否处理完所有样本
              if (this.processedSamples >= this.totalSamples) {
                this.decoder.flush().then(() => {
                  this.state = 'completed';
                  this.callbacks.onComplete();
                }).catch(error => {
                  this.callbacks.onError(error);
                });
              }
            } catch (error) {
              this.callbacks.onError(error as Error);
            }
          }
        }
      };

      // MP4Box flush完成回调 - 作为备用机制
      mp4boxFile.onFlush = () => {
        console.log('🎬 MP4Box onFlush called - 备用完成机制');
        // 如果还没有完成，则作为备用机制
        if (this.state !== 'completed' && this.decoder) {
          console.log('🎬 使用备用完成机制...');
          this.decoder.flush().then(() => {
            this.state = 'completed';
            console.log('✅ 备用机制：解码完成，调用onComplete');
            this.callbacks.onComplete();
          }).catch(error => {
            console.error('❌ 备用机制：解码器flush错误:', error);
            this.callbacks.onError(error);
          });
        }
      };

      // 添加错误处理
      mp4boxFile.onError = (error: any) => {
        console.error('❌ MP4Box error:', error);
        this.callbacks.onError(new Error(`MP4Box error: ${error}`));
      };

      // 开始解析
      console.log('🎬 开始解析MP4文件，大小:', arrayBuffer.byteLength);
      (arrayBuffer as any).fileStart = 0;
      mp4boxFile.appendBuffer(arrayBuffer);

      // 开始提取样本
      setTimeout(() => {
        console.log('🎬 开始提取样本...');
        mp4boxFile.flush(); // 触发样本提取
      }, 100); // 减少延迟

    } catch (error) {
      this.state = 'error';
      this.callbacks.onError(error as Error);
    }
  }

  // 获取视频信息
  getVideoInfo(): VideoInfo | null {
    return this.videoInfo;
  }

  // 获取当前状态
  getState(): DecoderState {
    return this.state;
  }

  // 清理资源
  dispose(): void {
    if (this.decoder) {
      this.decoder.close();
      this.decoder = null;
    }
    this.state = 'idle';
    this.frameIndex = 0;
    this.videoInfo = null;
  }
}
