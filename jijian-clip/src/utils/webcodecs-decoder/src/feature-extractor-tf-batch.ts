// 批处理优化的 TensorFlow.js 特征提取器
declare const tf: any;
import { FrameDifference } from './types.js';

interface BatchResult {
  frameIndex: number;
  features: FrameDifference;
}

interface PendingFrame {
  videoFrame: VideoFrame;
  frameIndex: number;
  timestamp: number;
  resolve: (result: FrameDifference | null) => void;
  reject: (error: Error) => void;
}

export class BatchFeatureExtractor {
  public isBatchProcessor = true; // 标识为批处理器
  private batchSize: number; // 批处理大小
  private frameQueue: PendingFrame[] = []; // 帧队列
  private isProcessing = false;
  private isTFReady = false;
  private isDecodingComplete = false; // 解码是否完成

  // GPU内存优化
  private prevBatchLastFrame: any = null; // 上一批的最后一帧，用于计算差异
  private textureKernel: any = null;
  private readonly RESIZE_WIDTH = 256;

  // 重用的Sobel算子，避免重复创建
  private sobelX: any = null;
  private sobelY: any = null;

  // 重用的canvas元素，避免重复创建
  private reusableCanvas: HTMLCanvasElement | null = null;
  private reusableCtx: CanvasRenderingContext2D | null = null;

  // 性能统计
  private batchCount = 0;
  private totalBatchTime = 0;

  constructor(batchSize: number = 8) {
    this.batchSize = Math.max(1, Math.min(batchSize, 32)); // 限制在1-32之间
    console.log('🚀 初始化批处理特征提取器，批大小:', this.batchSize);
  }

  // 时间戳精度处理函数（与decoder保持一致）
  private roundTime(value: number, precision: number = 3): number {
    const multiplier = Math.pow(10, precision);
    return Math.round(value * multiplier) / multiplier;
  }

  async initialize(): Promise<void> {
    await this.loadTF();

    // 初始化Sobel算子核
    this.textureKernel = tf.tensor2d([
      [-1, 0, 1],
      [-2, 0, 2],
      [-1, 0, 1]
    ], [3, 3], 'float32');

    // 初始化批处理用的Sobel算子
    this.sobelX = tf.tensor4d([
      [
        [[-1]],
        [[0]],
        [[1]]
      ],
      [
        [[-2]],
        [[0]],
        [[2]]
      ],
      [
        [[-1]],
        [[0]],
        [[1]]
      ]
    ], [3, 3, 1, 1]);

    this.sobelY = tf.tensor4d([
      [
        [[-1]],
        [[-2]],
        [[-1]]
      ],
      [
        [[0]],
        [[0]],
        [[0]]
      ],
      [
        [[1]],
        [[2]],
        [[1]]
      ]
    ], [3, 3, 1, 1]);

    // 初始化重用的canvas
    this.reusableCanvas = document.createElement('canvas');
    this.reusableCtx = this.reusableCanvas.getContext('2d')!;

    console.log('✅ 批处理GPU特征提取器已启用');
  }

  private async loadTF(): Promise<void> {
    try {
      if (this.isTFReady && tf) return;

      console.log('🔄 开始加载TensorFlow.js...');

      // 动态加载 TensorFlow.js
      const tfLib = await loadTensorFlow();
      tf = tfLib; // 设置全局引用

      console.log('✅ TensorFlow.js库加载成功，检查后端...');

      const currentBackend = tf.getBackend();
      console.log('🔍 当前TensorFlow.js后端:', currentBackend);

      if (currentBackend !== 'webgl') {
        console.log(`🔄 切换TensorFlow.js后端从 ${currentBackend} 到 webgl`);
        await tf.setBackend('webgl');
      }

      await tf.ready();
      this.isTFReady = true;
      console.log(`✅ TensorFlow.js ${tf.getBackend()} backend ready for batch processing`);
    } catch (error) {
      console.error('❌ TensorFlow.js initialization failed:', error);
      throw error;
    }
  }

  async processFrame(videoFrame: VideoFrame, frameIndex: number): Promise<FrameDifference | null> {
    if (!this.isTFReady) {
      await this.loadTF();
    }

    return new Promise((resolve, reject) => {
      // 将帧加入队列
      this.frameQueue.push({
        videoFrame,
        frameIndex,
        timestamp: this.roundTime((videoFrame.timestamp || 0) / 1_000_000), // 转换微秒为秒，保持3位小数精度
        resolve,
        reject
      });

      // 检查是否可以开始批处理
      this.checkBatchProcessing();
    });
  }

  private checkBatchProcessing(): void {
    // 如果正在处理，跳过
    if (this.isProcessing) {
      return;
    }

    // 如果队列中有足够的帧，或者解码已完成且队列不为空，则开始处理
    if (this.frameQueue.length >= this.batchSize ||
        (this.isDecodingComplete && this.frameQueue.length > 0)) {
      setTimeout(() => this.processBatch(), 0);
    }
  }

  private async processBatch(): Promise<void> {
    if (this.isProcessing || this.frameQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    const batchStartTime = performance.now();
    let batchTensors: any = null; // 用于确保在finally中释放

    try {
      // 确定批大小：如果解码完成，处理所有剩余帧；否则处理一批
      const actualBatchSize = this.isDecodingComplete ?
        this.frameQueue.length :
        Math.min(this.batchSize, this.frameQueue.length);

      const batchFrames = this.frameQueue.splice(0, actualBatchSize);
      console.log(`🚀 开始批处理 ${batchFrames.length} 帧 (批次 ${this.batchCount + 1})`);

      // 批量转换为张量
      batchTensors = await this.framesToBatchTensor(batchFrames);

      // 批量计算特征
      const batchResults = await this.computeBatchFeatures(batchTensors, batchFrames);

      // 分发结果给对应的Promise
      batchResults.forEach((result, index) => {
        if (result) {
          batchFrames[index].resolve(result.features);
        } else {
          batchFrames[index].resolve(null);
        }
      });

      // 性能统计
      const batchTime = performance.now() - batchStartTime;
      this.batchCount++;
      this.totalBatchTime += batchTime;
      const avgBatchTime = this.totalBatchTime / this.batchCount;
      const framesPerSecond = (batchFrames.length / batchTime) * 1000;

      console.log(`✅ 批处理完成: ${batchTime.toFixed(1)}ms, ${framesPerSecond.toFixed(1)} fps, 平均: ${avgBatchTime.toFixed(1)}ms`);

      // 打印内存使用情况
      console.log(`🧠 TensorFlow.js 内存: ${tf.memory().numTensors} 张量, ${(tf.memory().numBytes / 1024 / 1024).toFixed(1)}MB`);

    } catch (error) {
      console.error('❌ 批处理错误:', error);
      // 将错误传播给所有等待的Promise，并关闭VideoFrame
      const actualBatchSize = this.isDecodingComplete ?
        this.frameQueue.length :
        Math.min(this.batchSize, this.frameQueue.length);
      const errorFrames = this.frameQueue.splice(0, actualBatchSize);
      errorFrames.forEach(frame => {
        frame.reject(error as Error);
        // 关闭VideoFrame释放内存
        try {
          frame.videoFrame.close();
        } catch (e) {
          // 忽略已关闭的错误
        }
      });
    } finally {
      // 🔥 关键修复：确保batchTensor被释放
      if (batchTensors) {
        try {
          batchTensors.dispose();
          console.log('🧹 已释放批处理张量');
        } catch (e) {
          console.warn('⚠️ 释放批处理张量时出错:', e);
        }
      }

      this.isProcessing = false;

      // 如果还有待处理的帧，继续处理
      this.checkBatchProcessing();
    }
  }

  private async framesToBatchTensor(frames: PendingFrame[]): Promise<any> {
    const closedFrames: boolean[] = new Array(frames.length).fill(false);

    try {
      return tf.tidy(() => {
        const tensors: any[] = [];

        // 批量转换VideoFrame到张量
        for (let i = 0; i < frames.length; i++) {
          const frame = frames[i];

          // 🔥 使用重用的canvas，避免重复创建
          if (!this.reusableCanvas || !this.reusableCtx) {
            throw new Error('Canvas not initialized');
          }

          // 调整canvas尺寸
          this.reusableCanvas.width = frame.videoFrame.displayWidth;
          this.reusableCanvas.height = frame.videoFrame.displayHeight;

          this.reusableCtx.drawImage(frame.videoFrame, 0, 0);

          // 转换为张量并调整尺寸
          const tensor = tf.browser.fromPixels(this.reusableCanvas);
          const aspectRatio = frame.videoFrame.displayHeight / frame.videoFrame.displayWidth;
          const targetHeight = Math.round(this.RESIZE_WIDTH * aspectRatio);
          const resized = tf.image.resizeBilinear(tensor, [targetHeight, this.RESIZE_WIDTH]);

          tensors.push(resized);

          // 立即释放VideoFrame内存，因为已经转换为张量
          frame.videoFrame.close();
          closedFrames[i] = true;
        }

        // 堆叠为批张量 [batchSize, height, width, channels]
        return tf.stack(tensors);
      });
    } catch (error) {
      // 如果出错，关闭所有未关闭的VideoFrame
      for (let i = 0; i < frames.length; i++) {
        if (!closedFrames[i]) {
          try {
            frames[i].videoFrame.close();
          } catch (e) {
            // 忽略关闭错误
          }
        }
      }
      throw error;
    }
  }

  private async computeBatchFeatures(batchTensor: any, frames: PendingFrame[]): Promise<(BatchResult | null)[]> {
    const results: (BatchResult | null)[] = [];

    // 准备前一帧张量 [batchSize, height, width, channels]
    let prevFrames: any;
    let currFrames: any;

    if (!this.prevBatchLastFrame) {
      // 第一批：第一帧返回null，使用批内的前一帧
      results.push(null);

      if (frames.length > 1) {
        // 创建前一帧张量：[frame0, frame1, ..., frame(n-2)]
        prevFrames = tf.slice(batchTensor, [0, 0, 0, 0], [frames.length - 1, -1, -1, -1]);
        // 创建当前帧张量：[frame1, frame2, ..., frame(n-1)]
        currFrames = tf.slice(batchTensor, [1, 0, 0, 0], [frames.length - 1, -1, -1, -1]);

        // 批量计算特征差异
        const batchFeatures = this.computeBatchFrameDifferences(prevFrames, currFrames);

        // 解包结果
        for (let i = 0; i < frames.length - 1; i++) {
          results.push({
            frameIndex: frames[i + 1].frameIndex,
            features: {
              colorDiff: batchFeatures.colorDiffs[i],
              motionDiff: batchFeatures.motionDiffs[i],
              textureDiff: batchFeatures.textureDiffs[i],
              frameIndex: frames[i + 1].frameIndex,
              timestamp: frames[i + 1].timestamp
            }
          });
        }

        // 释放临时张量
        prevFrames.dispose();
        currFrames.dispose();
      }

      // 保存最后一帧
      this.prevBatchLastFrame = tf.tidy(() => tf.slice(batchTensor, [frames.length - 1, 0, 0, 0], [1, -1, -1, -1]).squeeze().clone());
    } else {
      // 后续批次：使用上一批的最后一帧 + 当前批的所有帧

      // 🔥 使用tf.tidy管理临时张量
      const batchFeatures = tf.tidy(() => {
        // 扩展上一帧到批维度
        const expandedPrevFrame = tf.expandDims(this.prevBatchLastFrame, 0);
        // 取当前批的前n-1帧
        const batchPrevFrames = tf.slice(batchTensor, [0, 0, 0, 0], [frames.length - 1, -1, -1, -1]);
        // 拼接：[lastFrameFromPrevBatch, frame0, frame1, ..., frame(n-2)]
        prevFrames = tf.concat([expandedPrevFrame, batchPrevFrames], 0);

        // 当前帧：[frame0, frame1, ..., frame(n-1)]
        currFrames = batchTensor;

        // 批量计算特征差异
        return this.computeBatchFrameDifferences(prevFrames, currFrames);
      });

      // 解包结果
      for (let i = 0; i < frames.length; i++) {
        results.push({
          frameIndex: frames[i].frameIndex,
          features: {
            colorDiff: batchFeatures.colorDiffs[i],
            motionDiff: batchFeatures.motionDiffs[i],
            textureDiff: batchFeatures.textureDiffs[i],
            frameIndex: frames[i].frameIndex,
            timestamp: frames[i].timestamp
          }
        });
      }

      // 更新参考帧
      this.prevBatchLastFrame.dispose();
      this.prevBatchLastFrame = tf.tidy(() => tf.slice(batchTensor, [frames.length - 1, 0, 0, 0], [1, -1, -1, -1]).squeeze().clone());
    }

    return results;
  }

  // 批量计算帧间差异 - 真正的GPU并行计算
  private computeBatchFrameDifferences(prevFrames: any, currFrames: any): {
    colorDiffs: number[],
    motionDiffs: number[],
    textureDiffs: number[]
  } {
    return tf.tidy(() => {
      // 批量计算颜色差异
      const colorDiffs = this.calculateBatchColorDifference(prevFrames, currFrames);

      // 批量计算运动差异
      const motionDiffs = this.calculateBatchMotionDifference(prevFrames, currFrames);

      // 批量计算纹理差异
      const textureDiffs = this.calculateBatchTextureDifference(prevFrames, currFrames);

      return {
        colorDiffs: Array.from(colorDiffs.dataSync()),
        motionDiffs: Array.from(motionDiffs.dataSync()),
        textureDiffs: Array.from(textureDiffs.dataSync())
      };
    });
  }

  // 批量颜色差异计算
  private calculateBatchColorDifference(prevFrames: any, currFrames: any): any {
    return tf.tidy(() => {
      // 分离RGB通道 [batchSize, height, width, 3] -> 3 x [batchSize, height, width, 1]
      const [prevR, prevG, prevB] = tf.split(prevFrames, 3, 3);
      const [currR, currG, currB] = tf.split(currFrames, 3, 3);

      // 批量计算每个通道的均值 [batchSize, 1]
      const prevRMean = tf.mean(prevR, [1, 2, 3]);
      const prevGMean = tf.mean(prevG, [1, 2, 3]);
      const prevBMean = tf.mean(prevB, [1, 2, 3]);

      const currRMean = tf.mean(currR, [1, 2, 3]);
      const currGMean = tf.mean(currG, [1, 2, 3]);
      const currBMean = tf.mean(currB, [1, 2, 3]);

      // 批量计算差异
      const rDiff = tf.square(tf.sub(currRMean, prevRMean));
      const gDiff = tf.square(tf.sub(currGMean, prevGMean));
      const bDiff = tf.square(tf.sub(currBMean, prevBMean));

      // 批量计算欧几里得距离并归一化
      const colorDiff = tf.sqrt(tf.add(tf.add(rDiff, gDiff), bDiff));
      return tf.div(colorDiff, 255.0);
    });
  }

  // 批量运动差异计算
  private calculateBatchMotionDifference(prevFrames: any, currFrames: any): any {
    return tf.tidy(() => {
      // 批量转换为灰度
      const prevGray = this.batchRgbToGrayscale(prevFrames);
      const currGray = this.batchRgbToGrayscale(currFrames);

      // 批量计算像素差异
      const diff = tf.abs(tf.sub(currGray, prevGray));

      // 批量计算均值
      return tf.mean(diff, [1, 2, 3]);
    });
  }

  // 批量纹理差异计算
  private calculateBatchTextureDifference(prevFrames: any, currFrames: any): any {
    return tf.tidy(() => {
      // 批量转换为灰度
      const prevGray = this.batchRgbToGrayscale(prevFrames);
      const currGray = this.batchRgbToGrayscale(currFrames);

      // 批量计算纹理特征（使用简化的梯度）
      const prevTexture = this.calculateBatchTextureFeature(prevGray);
      const currTexture = this.calculateBatchTextureFeature(currGray);

      // 批量计算纹理差异
      const diff = tf.abs(tf.sub(currTexture, prevTexture));
      return tf.mean(diff, [1, 2, 3]);
    });
  }

  // 批量RGB转灰度
  private batchRgbToGrayscale(frames: any): any {
    return tf.tidy(() => {
      const [r, g, b] = tf.split(frames, 3, 3);
      return tf.add(tf.add(tf.mul(r, 0.299), tf.mul(g, 0.587)), tf.mul(b, 0.114));
    });
  }

  // 批量纹理特征计算
  private calculateBatchTextureFeature(grayFrames: any): any {
    return tf.tidy(() => {
      // 🔥 使用重用的Sobel算子，避免重复创建
      if (!this.sobelX || !this.sobelY) {
        throw new Error('Sobel operators not initialized');
      }

      // 批量卷积计算梯度
      const gradX = tf.conv2d(grayFrames, this.sobelX, 1, 'same');
      const gradY = tf.conv2d(grayFrames, this.sobelY, 1, 'same');

      // 批量计算梯度幅值
      return tf.sqrt(tf.add(tf.square(gradX), tf.square(gradY)));
    });
  }

  private computeFrameDifference(prev: any, curr: any): Omit<FrameDifference, 'frameIndex' | 'timestamp'> {
    // 不使用 tf.tidy，让各个计算方法自己管理内存
    const colorDiff = this.calculateColorDifference(prev, curr);
    const motionDiff = this.calculateMotionDifference(prev, curr);
    const textureDiff = this.calculateTextureDifference(prev, curr);

    return { colorDiff, motionDiff, textureDiff };
  }

  private calculateColorDifference(prev: any, curr: any): number {
    return tf.tidy(() => {
      const [prevR, prevG, prevB] = tf.split(prev, 3, 2);
      const [currR, currG, currB] = tf.split(curr, 3, 2);

      const prevRMean = tf.mean(prevR);
      const prevGMean = tf.mean(prevG);
      const prevBMean = tf.mean(prevB);

      const currRMean = tf.mean(currR);
      const currGMean = tf.mean(currG);
      const currBMean = tf.mean(currB);

      const rDiff = tf.square(tf.sub(currRMean, prevRMean));
      const gDiff = tf.square(tf.sub(currGMean, prevGMean));
      const bDiff = tf.square(tf.sub(currBMean, prevBMean));

      const colorDiff = tf.sqrt(tf.add(tf.add(rDiff, gDiff), bDiff));
      return colorDiff.dataSync()[0] / 255.0;
    });
  }

  private calculateMotionDifference(prev: any, curr: any): number {
    return tf.tidy(() => {
      const prevGray = this.rgbToGrayscale(prev);
      const currGray = this.rgbToGrayscale(curr);
      const diff = tf.abs(prevGray.sub(currGray));
      return diff.mean().dataSync()[0];
    });
  }

  private calculateTextureDifference(prev: any, curr: any): number {
    return tf.tidy(() => {
      const prevGray = this.rgbToGrayscale(prev);
      const currGray = this.rgbToGrayscale(curr);
      
      const sobelPrev = this.sobelEdgeDetection(prevGray);
      const sobelCurr = this.sobelEdgeDetection(currGray);
      
      const diff = tf.abs(sobelPrev.sub(sobelCurr));
      return diff.mean().dataSync()[0];
    });
  }

  private rgbToGrayscale(rgb: any): any {
    return tf.tidy(() => {
      const [r, g, b] = tf.split(rgb, 3, 2);
      return r.mul(0.299).add(g.mul(0.587)).add(b.mul(0.114)).squeeze();
    });
  }

  private sobelEdgeDetection(gray: any): any {
    return tf.tidy(() => {
      const batched = gray.reshape([1, ...gray.shape, 1]);
      const gx = tf.conv2d(batched, this.textureKernel.reshape([3, 3, 1, 1]), 1, 'same');
      const gy = tf.conv2d(batched, this.textureKernel.transpose().reshape([3, 3, 1, 1]), 1, 'same');
      return tf.sqrt(gx.square().add(gy.square())).squeeze();
    });
  }

  // 强制处理剩余帧（在视频结束时调用）
  async flush(): Promise<void> {
    console.log(`🔄 解码完成，标记为完成状态`);
    this.isDecodingComplete = true;

    // 触发处理剩余帧
    this.checkBatchProcessing();

    // 等待所有帧处理完成
    while (this.frameQueue.length > 0 || this.isProcessing) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    console.log(`✅ 所有帧处理完成`);
  }

  dispose(): void {
    console.log('🧹 开始清理批处理特征提取器...');

    // 释放GPU张量
    if (this.prevBatchLastFrame) {
      this.prevBatchLastFrame.dispose();
      this.prevBatchLastFrame = null;
    }
    if (this.textureKernel) {
      this.textureKernel.dispose();
      this.textureKernel = null;
    }

    // 🔥 释放重用的Sobel算子
    if (this.sobelX) {
      this.sobelX.dispose();
      this.sobelX = null;
    }
    if (this.sobelY) {
      this.sobelY.dispose();
      this.sobelY = null;
    }

    // 🔥 清理重用的canvas
    if (this.reusableCanvas) {
      // 清空canvas内容
      if (this.reusableCtx) {
        this.reusableCtx.clearRect(0, 0, this.reusableCanvas.width, this.reusableCanvas.height);
      }
      this.reusableCanvas = null;
      this.reusableCtx = null;
    }

    // 拒绝所有待处理的Promise并关闭VideoFrame
    this.frameQueue.forEach(frame => {
      // 使用特殊的错误类型，表示这是正常的dispose操作
      const disposeError = new Error('Feature extractor disposed');
      disposeError.name = 'FeatureExtractorDisposedError';
      frame.reject(disposeError);
      // 关闭VideoFrame释放内存
      try {
        frame.videoFrame.close();
      } catch (e) {
        // 忽略已关闭的错误
      }
    });
    this.frameQueue = [];

    // 清理TensorFlow.js变量
    tf.disposeVariables();

    // 打印最终内存状态
    const finalMemory = tf.memory();
    console.log(`📊 批处理统计: ${this.batchCount} 批次, 平均 ${(this.totalBatchTime / this.batchCount).toFixed(1)}ms/批`);
    console.log(`🧠 清理后TensorFlow.js内存: ${finalMemory.numTensors} 张量, ${(finalMemory.numBytes / 1024 / 1024).toFixed(1)}MB`);
  }
}
