<template>
  <div class="clip-menu-container" :style="{ width: containerWidth + 'px' }">
    <!-- 左侧菜单栏 -->
    <div class="menu-sidebar">
      <!-- 菜单项 -->
      <div class="menu-items">
        <div
          v-for="(item, index) in menuList"
          :key="item.name"
          class="menu-item"
          :class="{
            'active': isMenuItemActive(item),
            'ai-active': item.name === 'AI助手' && panelStates.ai
          }"
          @click="handleMenuClick(item, index)"
        >
          <div class="menu-icon">
            <Icon
              :icon="getMenuIcon(item.name)"
              :class="{
                'ai-icon': item.name === 'AI助手',
                'ai-connected': item.name === 'AI助手' && aiConnected
              }"
            />
          </div>
          <span class="menu-label">{{ item.name }}</span>
          <!-- AI助手状态指示器 -->
          <div
            v-if="item.name === 'AI助手'"
            class="ai-status-indicator"
            :class="{ 'connected': aiConnected, 'processing': aiProcessing }"
          ></div>
        </div>
      </div>

      <!-- 底部用户头像 -->
      <div class="user-avatar-wrapper">
        <UserAvatar />
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content-area" v-show="hasActivePanel" :style="{ width: contentWidth + 'px' }">
      <!-- 拖拽调整手柄 -->
      <div
        class="left-panel-resize-handle"
        @mousedown="startLeftPanelResize"
        title="拖拽调整面板宽度"
      ></div>

      <!-- 面板头部 -->
      <div class="content-header">
        <div class="flex items-center space-x-3">
          <span class="text-white font-medium">{{ currentPanelTitle }}</span>
          <div class="panel-width-indicator">
            {{ contentWidth }}px
          </div>
        </div>
      </div>

      <!-- 面板内容 -->
      <div class="content-body">
        <component :is="currentComponent" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { defineAsyncComponent } from 'vue';
import { Icon } from '@iconify/vue';

// 异步组件导入
const MediaLibrary = defineAsyncComponent(() => import('./components/mediaLibrary.vue'));
// const AudioLibrary = defineAsyncComponent(() => import('./components/audioLibrary.vue')); // 暂时隐藏音频功能
// const AudioEffects = defineAsyncComponent(() => import('./components/audioEffects.vue')); // 暂时隐藏音频功能
const TextLibrary = defineAsyncComponent(() => import('./components/textLibrary.vue'));
const FilterLibrary = defineAsyncComponent(() => import('./components/filterLibrary.vue'));

const UserAvatar = defineAsyncComponent(() => import('./components/UserAvatar.vue'));

// AI助手状态管理（简化版本，默认显示）
const aiVisible = ref(true);
const aiConnected = ref(false);
const aiProcessing = ref(false);

// 模拟AI助手状态
const aiStore = {
  isVisible: aiVisible,
  wsConnected: aiConnected,
  isProcessing: aiProcessing,
  toggleVisibility: () => {
    aiVisible.value = !aiVisible.value;
    // 通知父组件显示/隐藏AI助手
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('toggleAIAssistant', {
        detail: { visible: aiVisible.value }
      }));
    }
  }
};

// 菜单配置 - 添加面板类型和切换功能
const menuList = [
  { name: '媒体库', component: MediaLibrary, icon: 'mdi:folder-open', panelType: 'media' },
  // { name: '音频库', component: AudioLibrary, icon: 'mdi:music', panelType: 'audio' }, // 暂时隐藏音频功能
  // { name: '音频效果', component: AudioEffects, icon: 'mdi:tune', panelType: 'audioEffects' }, // 暂时隐藏音频功能
  { name: '文字', component: TextLibrary, icon: 'mdi:format-text', panelType: 'text' },
  { name: '滤镜', component: FilterLibrary, icon: 'mdi:auto-fix', panelType: 'filter' },
  { name: '重新处理', component: null, icon: 'mdi:refresh', panelType: 'reprocess', action: 'reprocessVideo' },
  { name: 'AI助手', component: null, icon: 'mdi:robot', panelType: 'ai', action: 'toggleAI' },
];

// 面板可见性状态管理
const panelStates = ref({
  media: false,
  // audio: false, // 暂时隐藏音频功能
  // audioEffects: false, // 暂时隐藏音频功能
  text: false,
  filter: false,
  ai: true // AI助手默认显示
});

// 🎯 左侧面板宽度管理
const menuSidebarWidth = 80; // 菜单栏固定宽度
const contentWidth = ref(200); // 内容区域宽度
const minContentWidth = 180;   // 最小宽度
const maxContentWidth = 400;   // 最大宽度
let isResizingLeftPanel = false; // 是否正在调整左侧面板大小

// 计算容器总宽度
const containerWidth = computed(() => {
  return hasActivePanel.value ? menuSidebarWidth + contentWidth.value : menuSidebarWidth;
});

// 当前激活的标签
const activeTab = ref(0);

// 当前组件
const currentComponent = computed(() => {
  return menuList[activeTab.value]?.component;
});

// 是否有激活的面板（排除AI助手）
const hasActivePanel = computed(() => {
  const { ai, ...otherPanels } = panelStates.value;
  return Object.values(otherPanels).some(state => state);
});

// 当前面板标题
const currentPanelTitle = computed(() => {
  if (activeTab.value >= 0 && activeTab.value < menuList.length) {
    return menuList[activeTab.value].name;
  }
  return '面板';
});

// 判断菜单项是否激活
const isMenuItemActive = (item: any) => {
  if (item.panelType === 'ai') {
    return panelStates.value.ai;
  }
  return panelStates.value[item.panelType as keyof typeof panelStates.value];
};

// 处理菜单点击 - 实现切换功能
const handleMenuClick = (item: any, index: number) => {
  console.log('🔄 菜单点击:', item.name, 'panelType:', item.panelType, 'index:', index);

  try {
    if (item.panelType === 'ai') {
      // AI助手特殊处理
      console.log('🤖 处理AI助手点击');
      toggleAIPanel();
    } else if (item.panelType === 'reprocess') {
      // 🎯 重新处理视频特殊处理
      console.log('🔄 处理重新处理视频点击');
      handleReprocessVideo();
    } else {
      // 普通面板切换逻辑
      console.log('📦 处理普通面板点击:', item.panelType);
      togglePanel(item.panelType, index);
    }
  } catch (error) {
    console.error('❌ 菜单点击处理失败:', error);
  }
};

// 切换AI面板
const toggleAIPanel = () => {
  panelStates.value.ai = !panelStates.value.ai;
  aiVisible.value = panelStates.value.ai;

  // 通知父组件
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('toggleAIAssistant', {
      detail: { visible: panelStates.value.ai }
    }));
  }

  console.log('🤖 AI助手面板切换:', panelStates.value.ai ? '显示' : '隐藏');
};

// 切换普通面板
const togglePanel = (panelType: string, index: number) => {
  const currentState = panelStates.value[panelType as keyof typeof panelStates.value];

  if (currentState) {
    // 如果当前面板已显示，则隐藏
    panelStates.value[panelType as keyof typeof panelStates.value] = false;
    activeTab.value = -1; // 清除激活状态
    console.log(`📦 隐藏面板: ${panelType}`);
  } else {
    // 隐藏其他面板，显示当前面板
    Object.keys(panelStates.value).forEach(key => {
      if (key !== 'ai') { // 保持AI助手状态不变
        panelStates.value[key as keyof typeof panelStates.value] = false;
      }
    });

    panelStates.value[panelType as keyof typeof panelStates.value] = true;
    activeTab.value = index;
    console.log(`📦 显示面板: ${panelType}`);
  }

  // 保存状态并通知父组件
  savePanelStates();
  emitPanelStateChange();
};

// 发送面板状态变化事件
const emitPanelStateChange = () => {
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('panelStateChange', {
      detail: {
        panelStates: panelStates.value,
        activeTab: activeTab.value
      }
    }));

    // 🎯 同时发送宽度变化事件
    emitWidthChange();
  }
};

// 🎯 处理重新处理视频
const handleReprocessVideo = async () => {
  console.log('🔄 开始重新处理视频');

  try {
    // 通知父组件开始重新处理
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('reprocessVideo', {
        detail: { action: 'start' }
      }));
    }
  } catch (error) {
    console.error('❌ 重新处理视频失败:', error);
  }
};

// 获取菜单图标
const getMenuIcon = (name: string) => {
  const iconMap: Record<string, string> = {
    '媒体库': 'mdi:folder-open',
    '音频库': 'mdi:music',
    '音频效果': 'mdi:tune',
    '文字': 'mdi:format-text',
    '滤镜': 'mdi:auto-fix',
    '重新处理': 'mdi:refresh',
    'AI助手': 'mdi:robot',
  };

  return iconMap[name] || 'mdi:folder-open';
};

// 🎯 左侧面板拖拽调整功能 - 优化版本
const startLeftPanelResize = (event: MouseEvent) => {
  event.preventDefault();
  event.stopPropagation();

  isResizingLeftPanel = true;

  const startX = event.clientX;
  const startWidth = contentWidth.value;
  const containerRect = document.querySelector('.clip-menu-container')?.getBoundingClientRect();

  console.log('🎯 开始调整左侧面板宽度', { startX, startWidth, containerRect });

  // 添加拖拽开始的视觉反馈
  document.body.classList.add('resizing-left-panel');

  // 创建拖拽遮罩，防止iframe等元素干扰
  const dragOverlay = document.createElement('div');
  dragOverlay.className = 'drag-overlay';
  dragOverlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    cursor: col-resize;
    background: transparent;
  `;
  document.body.appendChild(dragOverlay);

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizingLeftPanel) return;

    // 计算新宽度（向右拖拽增加宽度，向左拖拽减少宽度）
    const deltaX = e.clientX - startX;
    let newWidth = startWidth + deltaX;

    // 🔧 改进的边界处理：使用更精确的限制
    const windowWidth = window.innerWidth;
    const maxAllowedWidth = Math.min(maxContentWidth, windowWidth * 0.4); // 最大不超过窗口宽度的40%
    const minAllowedWidth = Math.max(minContentWidth, 150); // 最小宽度不少于150px

    // 平滑的边界限制，避免突然的跳跃
    if (newWidth < minAllowedWidth) {
      newWidth = minAllowedWidth;
    } else if (newWidth > maxAllowedWidth) {
      newWidth = maxAllowedWidth;
    }

    // 🔧 确保宽度变化是连续的，避免间隙
    const roundedWidth = Math.round(newWidth);
    if (Math.abs(roundedWidth - contentWidth.value) >= 1) {
      contentWidth.value = roundedWidth;

      // 实时发送宽度变化事件，确保布局同步更新
      emitWidthChangeImmediate();
    }
  };

  const handleMouseUp = (e: MouseEvent) => {
    if (!isResizingLeftPanel) return;

    isResizingLeftPanel = false;

    // 清理视觉反馈
    document.body.classList.remove('resizing-left-panel');

    // 移除拖拽遮罩
    if (dragOverlay && dragOverlay.parentNode) {
      dragOverlay.parentNode.removeChild(dragOverlay);
    }

    console.log('✅ 左侧面板宽度调整完成', {
      newWidth: contentWidth.value,
      finalPosition: e.clientX
    });

    // 保存宽度设置
    saveLeftPanelWidth();

    // 移除事件监听器
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);

    // 🔧 延迟触发最终的布局更新，确保没有间隙
    nextTick(() => {
      setTimeout(() => {
        emitWidthChange();
      }, 50);
    });
  };

  // 添加事件监听器
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

// 保存左侧面板宽度到localStorage
const saveLeftPanelWidth = () => {
  try {
    localStorage.setItem('leftPanelContentWidth', contentWidth.value.toString());

    // 🎯 发送宽度变化事件给父组件
    emitWidthChange();
  } catch (error) {
    console.warn('保存左侧面板宽度失败:', error);
  }
};

// 发送宽度变化事件
const emitWidthChange = () => {
  if (typeof window !== 'undefined') {
    const totalWidth = hasActivePanel.value ? menuSidebarWidth + contentWidth.value : menuSidebarWidth;
    window.dispatchEvent(new CustomEvent('leftPanelWidthChange', {
      detail: {
        width: totalWidth,
        contentWidth: contentWidth.value,
        menuWidth: menuSidebarWidth,
        hasActivePanel: hasActivePanel.value,
        timestamp: Date.now()
      }
    }));
    console.log('📡 发送左侧面板宽度变化事件:', {
      totalWidth,
      contentWidth: contentWidth.value,
      hasActivePanel: hasActivePanel.value
    });
  }
};

// 🔧 实时发送宽度变化事件（拖拽过程中使用）
const emitWidthChangeImmediate = () => {
  if (typeof window !== 'undefined') {
    const totalWidth = hasActivePanel.value ? menuSidebarWidth + contentWidth.value : menuSidebarWidth;
    window.dispatchEvent(new CustomEvent('leftPanelWidthChangeImmediate', {
      detail: {
        width: totalWidth,
        contentWidth: contentWidth.value,
        menuWidth: menuSidebarWidth,
        hasActivePanel: hasActivePanel.value
      }
    }));
  }
};

// 恢复左侧面板宽度
const restoreLeftPanelWidth = () => {
  try {
    const savedWidth = localStorage.getItem('leftPanelContentWidth');
    if (savedWidth) {
      const width = parseInt(savedWidth, 10);
      if (width >= minContentWidth && width <= maxContentWidth) {
        contentWidth.value = width;
        console.log('📦 恢复左侧面板宽度:', width);
      }
    }
  } catch (error) {
    console.warn('恢复左侧面板宽度失败:', error);
  }
};

// 监听AI助手状态更新事件
const handleAIAssistantStateUpdate = (event: CustomEvent) => {
  console.log('🔄 同步AI助手状态:', event.detail.visible);
  aiVisible.value = event.detail.visible;
  panelStates.value.ai = event.detail.visible;
};

// 初始化面板状态
const initializePanelStates = () => {
  // 从localStorage恢复状态
  try {
    const savedStates = localStorage.getItem('menu-panel-states');
    if (savedStates) {
      const states = JSON.parse(savedStates);
      Object.assign(panelStates.value, states);
    }

    // 同步AI助手状态
    const savedAIState = localStorage.getItem('ai-assistant-visible');
    if (savedAIState !== null) {
      const aiState = JSON.parse(savedAIState);
      panelStates.value.ai = aiState;
      aiVisible.value = aiState;
    }
  } catch (error) {
    console.warn('恢复面板状态失败:', error);
  }
};

// 保存面板状态
const savePanelStates = () => {
  localStorage.setItem('menu-panel-states', JSON.stringify(panelStates.value));
};

// 初始化面板状态（在组件创建时立即执行）
initializePanelStates();

// 恢复左侧面板宽度
restoreLeftPanelWidth();

// 发送初始宽度
nextTick(() => {
  emitWidthChange();
});

// 添加事件监听器（在组件创建时立即执行）
if (typeof window !== 'undefined') {
  window.addEventListener('updateAIAssistantState', handleAIAssistantStateUpdate as EventListener);
}

// 监听面板状态变化并保存
watch(panelStates, () => {
  savePanelStates();
}, { deep: true });

// 监听激活面板状态变化，用于动态调整容器宽度
watch(hasActivePanel, (newValue) => {
  console.log('📦 激活面板状态变化:', newValue ? '有激活面板' : '无激活面板');

  // 🎯 面板显示/隐藏时发送宽度变化事件
  nextTick(() => {
    emitWidthChange();
  });
}, { immediate: true });
</script>

<style lang="scss" scoped>
.clip-menu-container {
  display: flex;
  height: 100vh;
  background: #1a1a1a;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  transition: width 0.3s ease;
}

/* 左侧菜单栏 - 专业编辑器风格 */
.menu-sidebar {
  width: v-bind('menuSidebarWidth + "px"');
  background: var(--editor-bg-secondary);
  border-right: 1px solid var(--editor-border);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

/* 菜单项容器 - 专业编辑器风格 */
.menu-items {
  flex: 1;
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

/* 单个菜单项 - 专业编辑器风格 */
.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  margin: 0 4px;
  border-radius: var(--editor-radius);
  cursor: pointer;
  position: relative;
  transition: all var(--editor-transition);
  background: transparent;
  border: 1px solid transparent;
  min-height: 60px;

  /* 悬停状态 */
  &:hover {
    background: var(--editor-bg-tertiary);
    border-color: var(--editor-border-light);

    .menu-icon {
      color: var(--editor-text-primary);
    }

    .menu-label {
      color: var(--editor-text-primary);
    }
  }

  /* 激活状态 */
  &.active {
    background: var(--editor-accent);
    border-color: var(--editor-accent);

    .menu-icon {
      color: white;
    }

    .menu-label {
      color: white;
      font-weight: 500;
    }
  }

  /* AI助手激活状态 */
  &.ai-active {
    background: var(--editor-success);
    border-color: var(--editor-success);

    .menu-icon {
      color: white;
    }

    .menu-label {
      color: white;
      font-weight: 500;
    }
  }
}

/* 菜单图标 - 专业编辑器风格 */
.menu-icon {
  font-size: 20px;
  color: var(--editor-text-secondary);
  margin-bottom: 6px;
  transition: color var(--editor-transition);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;

  /* AI助手图标特殊样式 */
  &.ai-icon {
    &.ai-connected {
      color: var(--editor-success);
    }
  }
}

/* AI助手状态指示器 */
.ai-status-indicator {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--editor-text-muted);
  transition: background-color var(--editor-transition);
  z-index: 10;

  &.connected {
    background-color: var(--editor-success);
  }

  &.processing {
    background-color: var(--editor-warning);
  }
}

/* 菜单标签 - 专业编辑器风格 */
.menu-label {
  color: var(--editor-text-secondary);
  text-align: center;
  transition: color var(--editor-transition);
  font-size: 11px;
  font-weight: 400;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 用户头像包装器 - 专业编辑器风格 */
.user-avatar-wrapper {
  padding: 16px 0;
  border-top: 1px solid var(--editor-border);
  margin-top: auto;
}

/* 右侧内容区域 */
.content-area {
  background: var(--editor-panel-bg);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  transition: width var(--editor-transition);
}

/* 左侧面板拖拽调整样式 - 专业编辑器风格 */
.left-panel-resize-handle {
  position: absolute;
  right: 0;
  top: 0;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: var(--editor-border);
  transition: background-color var(--editor-transition);
  z-index: 20;
  pointer-events: auto;
  user-select: none;
}

.left-panel-resize-handle:hover {
  background: var(--editor-accent);
}

/* 🔧 拖拽过程中的全局样式 */
:global(body.resizing-left-panel) {
  cursor: col-resize !important;
  user-select: none !important;
  /* 防止文本选择和其他交互 */
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

:global(body.resizing-left-panel *) {
  cursor: col-resize !important;
  pointer-events: none !important;
}

/* 拖拽遮罩样式 */
:global(.drag-overlay) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  cursor: col-resize !important;
  background: transparent !important;
  pointer-events: auto !important;
}

/* 🔧 容器样式优化，防止布局跳跃 */
.clip-menu-container {
  /* 确保容器有明确的定位上下文 */
  position: relative;
  /* 防止内容溢出导致的布局问题 */
  overflow: hidden;
  /* 使用硬件加速优化性能 */
  transform: translateZ(0);
  will-change: width;
  /* 平滑的宽度变化 */
  transition: width 0.1s ease-out;
}

/* 🔧 内容区域样式优化 */
.content-area {
  /* 确保内容区域正确填充 */
  width: 100%;
  height: 100%;
  /* 防止内容溢出 */
  overflow: hidden;
  /* 使用flexbox确保正确的布局 */
  display: flex;
  flex-direction: column;
  /* 平滑的宽度变化 */
  transition: none; /* 拖拽时不使用过渡效果 */
}

/* 🔧 拖拽时禁用过渡效果，避免延迟 */
:global(body.resizing-left-panel) .clip-menu-container,
:global(body.resizing-left-panel) .content-area {
  transition: none !important;
}

/* 内容区域头部 */
.content-header {
  background: var(--editor-bg-secondary);
  border-bottom: 1px solid var(--editor-border);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

/* 内容区域主体 */
.content-body {
  flex: 1;
  overflow: hidden;
}

/* 面板宽度指示器 */
.panel-width-indicator {
  background: var(--editor-bg-tertiary);
  border: 1px solid var(--editor-border);
  border-radius: var(--editor-radius);
  padding: 2px 6px;
  font-family: monospace;
  font-size: 11px;
  color: var(--editor-text-secondary);
}

/* 拖拽时的全局样式 */
body.resizing-left-panel {
  cursor: col-resize !important;
  user-select: none !important;
}

body.resizing-left-panel * {
  cursor: col-resize !important;
  user-select: none !important;
}

/* 响应式设计 - 优化版本 */
@media (max-width: 1200px) {
  .menu-item {
    padding: 16px 10px;
    margin: 0 8px 4px 8px;
  }

  .menu-icon {
    font-size: 22px;
    width: 30px;
    height: 30px;
  }

  .menu-label {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .menu-items {
    padding: 20px 0;
    gap: 2px;
  }

  .menu-item {
    padding: 14px 8px;
    margin: 0 6px 2px 6px;
    border-radius: 10px;
  }

  .menu-icon {
    font-size: 20px;
    width: 28px;
    height: 28px;
    margin-bottom: 8px;
  }

  .menu-label {
    font-size: 10px;
    font-weight: 500;
    letter-spacing: 0.3px;
  }

  .user-avatar-wrapper {
    padding: 16px 0;
  }

  .content-header {
    padding: 10px 12px;
  }
}

@media (max-width: 480px) {
  .menu-item {
    padding: 12px 6px;
    margin: 0 4px 2px 4px;
  }

  .menu-icon {
    font-size: 18px;
    width: 26px;
    height: 26px;
    margin-bottom: 6px;
  }

  .menu-label {
    font-size: 9px;
    font-weight: 500;
  }
}

/* 移除深色主题重复样式，使用统一的编辑器变量 */
</style>
