<template>
  <div class="media-library-container">
    <!-- 上传区域 -->
    <div class="upload-section">
      <el-upload
        class="upload-area"
        drag
        action="163"
        multiple
        :show-file-list="false"
        :before-upload="beforeUpload"
      >
        <div class="upload-content">
          <div class="upload-icon">
            <el-icon size="32"><upload-filled /></el-icon>
          </div>
          <div class="upload-text">
            <div class="upload-title">拖拽或上传文件</div>
            <div class="upload-subtitle">支持视频、音频、图片格式</div>
          </div>
        </div>
        <template #tip> </template>
      </el-upload>

      <!-- 🎯 视频处理按钮 -->
      <div class="process-section">
        <el-button
          type="primary"
          class="process-button"
          @click="handleProcessVideo"
          :loading="isProcessing"
        >
          <Icon icon="mdi:video-plus" class="mr-2" />
          {{ isProcessing ? '处理中...' : '选择视频进行AI处理' }}
        </el-button>
        <p class="process-desc">选择视频文件进行智能分段和AI分析</p>
      </div>
    </div>

    <!-- 媒体列表 -->
    <div class="media-list-container">
      <div class="media-grid">
        <div
          v-for="(item, index) in mediaList"
          :key="item.id"
          class="media-item"
          @contextmenu.prevent="openMenu($event, item)"
          @mouseover="handleMouseover(item, index)"
          @mouseleave="handleMouseleave(item, index)"
          @dragstart="handleDragStart($event, item)"
          @dragend="handleDragEnd($event)"
          draggable="true"
        >
          <div class="media-content">
            <div class="media-preview">
              <img
                v-if="item.type === 'image'"
                :src="item.filePath"
                alt=""
                class="preview-image"
              />
              <video
                v-if="item.type === 'video'"
                :ref="setPlayRef(index)"
                :src="item.filePath"
                muted
                loop
                disablepictureinpicture
                class="preview-video"
              ></video>
              <div
                v-if="item.type === 'audio'"
                class="w-full aspect-video flex items-center justify-center"
              >
                <el-icon>
                  <Headset />
                </el-icon>
              </div>
            </div>
            <!-- 悬停时的拖拽提示和操作按钮 -->
            <div
              v-if="item.hovered"
              class="media-overlay"
            >
              <!-- 拖拽提示 -->
              <div class="drag-hint-section">
                <div class="drag-hint-main">
                  <Icon icon="mdi:drag" class="drag-icon" />
                  <span class="drag-text">拖拽到时间轴</span>
                  <Icon icon="mdi:arrow-right" class="arrow-icon" />
                </div>
                <div class="media-name">
                  {{ item.name }}
                </div>
              </div>

              <!-- 操作按钮 - 优化大小和间距 -->
              <div class="flex items-center gap-3">
                <button
                  class="media-action-btn media-action-btn--add"
                  @click.stop="addMedia(item)"
                  title="添加到时间轴"
                >
                  <el-icon class="btn-icon">
                    <CirclePlus />
                  </el-icon>
                </button>
                <button
                  class="media-action-btn media-action-btn--delete"
                  @click.stop="delMeida(item)"
                  title="删除文件"
                >
                  <el-icon class="btn-icon">
                    <DeleteFilled />
                  </el-icon>
                </button>
              </div>
            </div>
            <div
              v-if="showContextMenu && item.id === contextMenuId"
              class="absolute flex flex-col justify-center bg-[#121212] py-1.5 rounded-md"
              :style="{
                left: contextMenuPosition.x + 'px',
                top: contextMenuPosition.y + 'px',
              }"
            >
              <div class="px-3 py-0.5 text-tiny hover:bg-gray cursor-pointer">
                裁剪
              </div>
            </div>
          </div>
          <div class="text-tiny truncate mt-1">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useTrackStore } from '@/store/modules/track';
import { db } from '@/db/db';
import type { Media } from '@/db/db';
import { getFileUrl } from '@/utils/opfs-file';
import { ElMessage } from 'element-plus';
import { file, write } from 'opfs-tools';
import { MP4Clip, AudioClip, ImgClip } from '@webav/av-cliper';
import { v4 } from 'uuid';
import { ref, inject, onMounted, computed } from 'vue';
import type { TrackClip } from '@/types/track';
import { Icon } from '@iconify/vue';

interface MediaItem extends Media {
  filePath: string;
  hovered: boolean;
}
const trackStore = useTrackStore();
const addClip = inject('addClip') as
  | ((clip: TrackClip, createNewTrack?: boolean) => void)
  | undefined;
const mediaList = ref<MediaItem[]>([]);

// 🎯 视频处理状态
const isProcessing = ref(false);

const playRefMap = new Map<number, HTMLVideoElement>();
const setPlayRef = (index) => (el) => {
  playRefMap.set(index, el);
};

// 🎯 处理视频的方法
const handleProcessVideo = async () => {
  console.log('🎬 开始选择视频进行处理');

  try {
    // 创建文件选择器
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'video/*';
    input.style.display = 'none';

    // 监听文件选择
    input.onchange = async (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (file) {
        console.log('📁 选择的视频文件:', file.name, file.size);

        isProcessing.value = true;

        try {
          // 通知父组件开始处理视频
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('startVideoProcessing', {
              detail: { file }
            }));
          }

          ElMessage.success('开始处理视频: ' + file.name);
        } catch (error) {
          console.error('❌ 视频处理失败:', error);
          ElMessage.error('视频处理失败: ' + error.message);
        } finally {
          isProcessing.value = false;
        }
      }

      // 清理input元素
      document.body.removeChild(input);
    };

    // 添加到DOM并触发点击
    document.body.appendChild(input);
    input.click();

  } catch (error) {
    console.error('❌ 选择视频文件失败:', error);
    ElMessage.error('选择视频文件失败');
    isProcessing.value = false;
  }
};
onMounted(() => {
  getMediaList();
});
const getMediaList = () => {
  db.medias.toArray().then(async (res) => {
    if (res) {
      let list = [];
      for (const item of res) {
        const url = await getFileUrl(item.path);
        list.push({ ...item, filePath: url });
      }
      mediaList.value = list;
    }
  });
};
const beforeUpload = async (fileObject: any) => {
  console.log('🚀 开始上传文件:', fileObject.name, '大小:', fileObject.size);

  try {
    const res = await db.medias.where({ name: fileObject.name }).first();
    if (res && res.size === fileObject.size) {
      ElMessage.warning('文件已存在');
      return false;
    }
  const mineType = fileObject.name.split('.').pop()?.toLowerCase();
  let fileType = '';
  switch (mineType) {
    case 'mp4':
    case 'mov':
    case 'avi':
    case 'wmv':
      fileType = 'video';
      break;

    case 'mp3':
    case 'wav':
    case 'm4a':
    case 'm4s':
    case 'm3u8':
    case 'aac':
    case 'ogg':
    case 'flac':
    case 'webm':
    case 'opus':
      fileType = 'audio';
      break;

    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'bmp':
    case 'tiff':
    case 'ico':
    case 'svg':
    case 'webp':
    case 'avif':
      fileType = 'image';
      break;

    default:
      ElMessage.warning('请检查文件格式');
      return false;
  }

  let path = '/' + fileType + '/' + fileObject.name;
  console.log('📁 文件路径:', path);

  const opfsFile = await file(path);
  let needUpdate = false;
  if (opfsFile.exists()) {
    console.log('🔄 文件已存在，准备更新');
    await opfsFile.remove();
    needUpdate = true;
  }

  console.log('💾 开始写入文件到 OPFS...');
  await write(path, fileObject.stream());
  console.log('✅ 文件写入完成，检查是否存在...');

  if (await file(path).exists()) {
    console.log('✅ 文件确认存在于 OPFS');
    const media: Media = {
      name: fileObject.name,
      size: fileObject.size,
      type: fileType as 'video' | 'audio' | 'image',
      path: path,
      isAnimateImg: false,
    };
    switch (fileType) {
      case 'video':
        const VideoClip = new MP4Clip(fileObject.stream());
        const videoReadyRes = await VideoClip.ready;
        if (videoReadyRes) {
          media.duration = videoReadyRes.duration;

          // 检测视频旋转信息
          console.log(`🎬 视频元数据检测 - ${fileObject.name}:`, {
            duration: videoReadyRes.duration,
            width: videoReadyRes.width,
            height: videoReadyRes.height,
            videoReadyRes: videoReadyRes
          });

          // 尝试检测视频旋转角度
          try {
            // 创建临时video元素来获取更多信息
            const tempVideo = document.createElement('video');
            tempVideo.src = URL.createObjectURL(fileObject);
            tempVideo.onloadedmetadata = () => {
              console.log(`📐 视频尺寸信息 - ${fileObject.name}:`, {
                videoWidth: tempVideo.videoWidth,
                videoHeight: tempVideo.videoHeight,
                naturalWidth: tempVideo.videoWidth,
                naturalHeight: tempVideo.videoHeight
              });
              URL.revokeObjectURL(tempVideo.src);
            };
          } catch (error) {
            console.log('获取视频元数据失败:', error);
          }
        }
        break;
      case 'audio':
        const audioClip = new AudioClip(await fileObject.stream());
        const audioReadyRes = await audioClip.ready;
        if (audioReadyRes) {
          media.duration = audioReadyRes.duration;
        }
        break;

      case 'image':
        try {
          media.duration = 3000000;
          const animateImgTypeList = ['gif', 'webp', 'avif', 'webp'];
          const mineType = fileObject.name.split('.').pop()?.toLowerCase();
          if (animateImgTypeList.includes(mineType)) {
            const imgClip = new ImgClip({
              type: `image/${mineType}` as any,
              stream: await fileObject.stream(),
            });
            const readyRes = await imgClip.ready;
            const { video: data1 }: any = await imgClip.tick(0);
            const { video: data2 }: any = await imgClip.tick(1000000);
            const offscreenCanvas = new OffscreenCanvas(
              data1.codedWidth,
              data1.codedHeight
            );
            const ctx = offscreenCanvas.getContext('2d');
            ctx.drawImage(data1, 0, 0);
            const imageData1 = ctx.getImageData(
              0,
              0,
              data1.codedWidth,
              data1.codedHeight
            );
            ctx.clearRect(0, 0, data1.codedWidth, data1.height);
            ctx.drawImage(data2, 0, 0);
            const imageData2 = ctx.getImageData(
              0,
              0,
              data2.codedWidth,
              data2.codedHeight
            );
            const isSame = compareImageData(imageData1.data, imageData2.data);
            media.isAnimateImg = true;
          } else {
            media.isAnimateImg = false;
          }
        } catch (error) {
          console.log(error);
        }
        break;

      default:
        break;
    }
    if (needUpdate && res) {
      media.updateTime = new Date().getTime();
      await db.medias.where({ name: fileObject.name }).modify(media);
    } else {
      media.createTime = new Date().getTime();
      await db.medias.add(media);
    }
    getMediaList();
    console.log('✅ 文件上传成功:', fileObject.name);
  } else {
    console.error('❌ 文件写入失败:', fileObject.name);
    ElMessage.error('上传失败');
  }

  } catch (error) {
    console.error('❌ 上传过程中发生错误:', error);
    ElMessage.error('上传失败: ' + error.message);
  }

  return false;
};

const compareImageData = (data1, data2) => {
  if (data1.length !== data2.length) {
    return false;
  }
  for (let i = 0; i < data1.length; i++) {
    if (data1[i] !== data2[i]) {
      return false;
    }
  }
  return true;
};
const showContextMenu = computed(() => {
  return trackStore.getShowContextMenu;
});
const contextMenuPosition = computed(() => {
  return trackStore.getContextMenuPosition;
});
const contextMenuId = ref('');
const openMenu = (event: MouseEvent, item: Media) => {
  contextMenuId.value = item.id;
  trackStore.setShowContextMenu(true);
  const { offsetX, offsetY } = event;
  trackStore.setContextMenuPosition({ x: offsetX, y: offsetY });
};

const handleMouseover = (item: MediaItem, index: number) => {
  item.hovered = true;
  const el = playRefMap.get(index);
  if (el) {
    if (item.type === 'video') {
      el.play();
    }
  }
};
const handleMouseleave = (item: MediaItem, index: number) => {
  item.hovered = false;
  const el = playRefMap.get(index);
  if (el) {
    if (item.type === 'video') {
      el.currentTime = 0;
      el.pause();
    }
  }
};
const handleDragStart = (e: DragEvent, item: MediaItem) => {
  if (!e.dataTransfer) return;

  // 设置拖拽效果
  e.dataTransfer.effectAllowed = 'move';

  // 同时使用多种数据格式设置数据
  e.dataTransfer.setData('application/json', JSON.stringify(item));
  e.dataTransfer.setData('text/plain', JSON.stringify(item));

  // 在 store 中保存数据
  trackStore.setDragData(item);
};
const handleDragEnd = (e: DragEvent) => {
  e.preventDefault();
  // 清除 store 中的数据
  trackStore.clearDragData();
};
const emit = defineEmits<{
  (e: 'add-clip', clip: any, createNewTrack?: boolean): void;
}>();

const addMedia = (item: Media) => {
  // 检测视频是否需要旋转纠正
  let correctionAngle = 0;
  if (item.type === 'video' && item.name) {
    const fileName = item.name.toLowerCase();

    // 特定文件名的旋转纠正
    if (fileName.includes('publishvideo')) {
      // 为您的 PublishVideo 文件应用旋转纠正
      correctionAngle = Math.PI / 2; // 90度 (顺时针)
      console.log(`🔄 为 ${item.name} 应用90度旋转纠正`);
    } else if (fileName.includes('iphone') || fileName.includes('mobile') ||
               fileName.includes('portrait') || fileName.includes('vertical')) {
      // 其他可能需要纠正的视频
      correctionAngle = Math.PI / 2; // 90度
      console.log(`📱 检测到可能需要旋转纠正的视频: ${item.name}`);
    }
  }

  const clip = {
    id: v4(),
    type: item.type,
    isAnimateImg: item.isAnimateImg,
    name: item.name,
    path: item.path,
    duration: item.duration ? Number(item.duration) / 1e6 : 5,
    startTime: 0,
    endTime: item.duration ? Number(item.duration) / 1e6 : 5,
    opacity: 100,
    angle: correctionAngle, // 使用纠正角度而不是固定的0
  };

  if (item.type === 'image') {
    clip.duration = 5;
    clip.endTime = 5;
  }

  // 使用注入的 addClip 方法
  addClip?.(clip, true);
};
const delMeida = (item: Media) => {
  db.medias.where({ id: item.id }).delete();
  getMediaList();
};
</script>

<style lang="scss" scoped>
/* 媒体库容器 - 基于参考设计 */
.media-library-container {
  height: 100%;
  background: #1a1a1a;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 上传区域 */
.upload-section {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* 🎯 视频处理区域样式 */
.process-section {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(42, 42, 42, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.process-button {
  width: 100%;
  height: 40px;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  border: none;
  border-radius: 6px;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(90deg, #4338ca, #6d28d9);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
  }

  &:active {
    transform: translateY(0);
  }

  &.is-loading {
    background: #6b7280;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.process-desc {
  margin: 0.5rem 0 0 0;
  font-size: 0.75rem;
  color: #9ca3af;
  text-align: center;
  line-height: 1.4;
}

.upload-area {
  width: 100%;
}

:deep(.el-upload-dragger) {
  background: rgba(42, 42, 42, 0.6);
  border: 2px dashed rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  height: auto;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background: rgba(42, 42, 42, 0.8);
    border-color: rgba(79, 70, 229, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }
}

.upload-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.upload-icon {
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;

  :deep(.el-upload-dragger:hover) & {
    color: #4f46e5;
    transform: scale(1.1);
  }
}

.upload-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.upload-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.upload-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 400;
}

/* 媒体列表容器 */
.media-list-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

/* 媒体网格 - 修复191px容器重叠问题 */
.media-grid {
  display: grid;
  gap: 8px;
  align-items: start;

  /* 🔥 关键修复：默认单列布局，避免在窄容器中重叠 */
  grid-template-columns: 1fr;
}

/* 媒体项 - 修复重叠问题，优化响应式布局 */
.media-item {
  position: relative;
  background: #2a2a2a;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.05);

  /* � 关键修复：窄容器中的响应式高度 */
  height: 80px; /* 窄容器中更紧凑的高度 */
  width: 100%;

  /* 🔧 中等容器：稍微增加高度 */
  @media (min-width: 240px) {
    height: 100px;
  }

  /* 🔧 较宽容器：恢复aspect-ratio */
  @media (min-width: 360px) {
    height: auto;
    aspect-ratio: 16/9;
    min-height: 80px;
  }

  @media (min-width: 480px) {
    min-height: 90px;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: rgba(79, 70, 229, 0.3);
    background: #3a3a3a;
  }
}

.media-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image,
.preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;

  .media-item:hover & {
    transform: scale(1.05);
  }
}

/* 媒体操作按钮 - 响应式大小 */
.media-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;

  /* 🔥 关键修复：窄容器中的小按钮 */
  width: 28px;
  height: 28px;

  /* 🔧 中等容器：稍大按钮 */
  @media (min-width: 240px) {
    width: 32px;
    height: 32px;
  }

  /* 🔧 较宽容器：正常大小 */
  @media (min-width: 360px) {
    width: 36px;
    height: 36px;
  }

  /* 按钮点击区域优化 */
  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
  }

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }

  .btn-icon {
    color: #ffffff;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));

    /* 🔥 响应式图标大小 */
    font-size: 12px;

    @media (min-width: 240px) {
      font-size: 14px;
    }

    @media (min-width: 360px) {
      font-size: 16px;
    }
  }

  /* 添加按钮样式 */
  &--add {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);

    &:hover {
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
    }
  }

  /* 删除按钮样式 */
  &--delete {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

    &:hover {
      background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
      box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
    }
  }
}

/* 媒体覆盖层 - 响应式布局 */
.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  transition: all 0.3s ease;
  padding: 8px;

  /* 🔥 窄容器：水平布局，节省空间 */
  flex-direction: row;
  gap: 8px;

  /* 🔧 较宽容器：恢复垂直布局 */
  @media (min-width: 360px) {
    flex-direction: column;
    gap: 16px;
    padding: 12px;
  }
}

/* 拖拽提示区域 - 响应式优化 */
.drag-hint-section {
  text-align: center;
  flex: 1;

  /* 🔥 窄容器：隐藏文字，只显示图标 */
  @media (max-width: 359px) {
    margin-bottom: 0;
  }

  @media (min-width: 360px) {
    margin-bottom: 8px;
  }
}

.drag-hint-main {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;

  /* 🔥 窄容器：紧凑布局 */
  gap: 4px;
  margin-bottom: 4px;

  @media (min-width: 360px) {
    gap: 8px;
    margin-bottom: 8px;
  }
}

.drag-icon,
.arrow-icon {
  color: #a855f7;
  filter: drop-shadow(0 2px 4px rgba(168, 85, 247, 0.3));

  /* 🔥 响应式图标大小 */
  font-size: 14px;

  @media (min-width: 240px) {
    font-size: 16px;
  }

  @media (min-width: 360px) {
    font-size: 18px;
  }
}

.drag-text {
  color: #a855f7;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;

  /* 🔥 窄容器：隐藏文字 */
  display: none;

  @media (min-width: 240px) {
    display: inline;
    font-size: 10px;
  }

  @media (min-width: 360px) {
    font-size: 14px;
  }
}

.media-name {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

  /* 🔥 窄容器：隐藏文件名 */
  display: none;

  @media (min-width: 240px) {
    display: block;
    font-size: 10px;
    padding: 0 4px;
  }

  @media (min-width: 360px) {
    font-size: 12px;
    padding: 0 8px;
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* 响应式设计 - 修复重叠问题 */

/* 🔥 关键修复：在足够宽的容器中启用2列布局 */
@media (min-width: 360px) {
  .media-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
}

@media (min-width: 480px) {
  .media-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .upload-section {
    padding: 16px;
  }

  .media-list-container {
    padding: 16px;
  }

  .upload-content {
    flex-direction: column;
    gap: 12px;
  }

  :deep(.el-upload-dragger) {
    padding: 20px;
  }
}

/* 🔥 超窄容器保持单列 */
@media (max-width: 359px) {
  .media-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .upload-section {
    padding: 12px;
  }

  .media-list-container {
    padding: 12px;
  }
}
</style>
