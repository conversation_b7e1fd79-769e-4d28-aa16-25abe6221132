<template>
  <div class="jijian-upload-page min-h-screen bg-[var(--bg-light)]">
    <!-- 顶部导航 -->
    <JijianHeader
      :is-authenticated="userStore.isLoggedIn"
      :user-name="userStore.userName"
      :user-avatar="userStore.userAvatar"
      @login-click="showLoginModal = true"
      @register-click="showRegisterModal = true"
      @upload-click="() => {}"
      @user-command="handleUserCommand"
    />

    <!-- 登录弹窗 -->
    <JijianLoginModal
      v-model="showLoginModal"
      mode="login"
      @login-success="handleLoginSuccess"
    />

    <!-- 注册弹窗 -->
    <JijianLoginModal
      v-model="showRegisterModal"
      mode="register"
      @register-success="handleRegisterSuccess"
    />

    <!-- 主要内容区域 -->
    <main class="main-content pt-24">
      <!-- 页面标题区域 -->
      <div class="upload-header bg-white shadow-sm">
        <div class="max-w-4xl mx-auto px-4 py-8">
          <div class="text-center">
            <h1 class="font-bold text-[var(--text-dark)] mb-4 flex items-center justify-center" style="font-size: 3.75rem;">
              <el-icon class="mr-3 text-[var(--primary)] text-4xl"><Upload /></el-icon>
              {{ $t('videoUpload.title') }}
            </h1>
            <p class="text-[var(--text-light)] max-w-2xl mx-auto" style="font-size: 1.25rem; line-height: 1.75rem;">
              {{ $t('videoUpload.supportedFormats') }}
            </p>
          </div>
        </div>
      </div>

      <div class="upload-content max-w-4xl mx-auto px-4 py-8">
        <!-- 处理警告提示 -->
        <div v-if="showProcessingWarning" class="processing-warning jijian-card p-6 mb-8 border-l-4 border-orange-500">
          <div class="flex items-start space-x-4">
            <div class="warning-icon">
              <el-icon class="text-2xl text-orange-500"><Warning /></el-icon>
            </div>
            <div class="warning-content">
              <h3 class="font-semibold text-[var(--text-dark)] mb-2">正在处理视频</h3>
              <p class="text-[var(--text-light)]">
                请勿关闭或切换页面，否则处理将中断。处理完成前请保持页面打开。
              </p>
            </div>
          </div>
        </div>

        <!-- WebCodecs 支持检查 -->
        <div v-if="!webCodecsSupported" class="support-warning jijian-card p-6 mb-8 border-l-4 border-yellow-500">
          <div class="flex items-start space-x-4">
            <div class="warning-icon">
              <el-icon class="text-2xl text-yellow-500"><InfoFilled /></el-icon>
            </div>
            <div class="warning-content">
              <h3 class="font-semibold text-[var(--text-dark)] mb-2">浏览器兼容性提示</h3>
              <p class="text-[var(--text-light)]">
                当前浏览器不支持 WebCodecs API，将使用基础处理模式。
                <br>推荐使用 Chrome 94+ 或 Edge 94+ 以获得最佳体验。
              </p>
            </div>
          </div>
        </div>

        <!-- 上传区域 -->
        <div
          class="upload-zone bg-white rounded-2xl shadow-[var(--shadow)] p-16 text-center border-2 border-dashed transition-all duration-300 cursor-pointer relative overflow-hidden"
          :class="[
            isDragging
              ? 'border-[var(--primary)] bg-[var(--primary)]/5 shadow-2xl scale-105'
              : 'border-gray-300 hover:border-[var(--primary)] hover:bg-gray-50 hover:shadow-xl'
          ]"
          @dragover.prevent="handleDragOver"
          @dragleave="handleDragLeave"
          @drop.prevent="handleDrop"
          @click="handleFileSelect"
        >
          <!-- 背景装饰 -->
          <div class="absolute top-0 right-0 w-32 h-32 bg-[var(--primary)]/5 rounded-full blur-2xl"></div>
          <div class="absolute bottom-0 left-0 w-24 h-24 bg-blue-500/5 rounded-full blur-xl"></div>

          <div class="upload-content-inner relative z-10">
            <!-- 上传图标 -->
            <div class="upload-icon-container mb-8">
              <div class="w-24 h-24 rounded-full bg-gradient-to-br from-[var(--primary)]/20 to-[var(--primary)]/10 flex items-center justify-center mx-auto mb-4 transform transition-transform duration-300 hover:scale-110">
                <el-icon class="text-5xl text-[var(--primary)]"><Upload /></el-icon>
              </div>
              <div class="flex justify-center space-x-2">
                <div class="w-2 h-2 bg-[var(--primary)] rounded-full animate-bounce" style="animation-delay: 0s"></div>
                <div class="w-2 h-2 bg-[var(--primary)] rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-[var(--primary)] rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              </div>
            </div>

            <!-- 主标题 -->
            <h3 class="text-3xl font-bold text-[var(--text-dark)] mb-4">
              拖拽视频文件到此处
            </h3>

            <!-- 副标题 -->
            <p class="text-[var(--text-light)] mb-8 max-w-lg mx-auto text-lg">
              或点击选择文件开始您的AI视频剪辑之旅
            </p>

            <!-- 上传按钮 -->
            <el-button
              type="primary"
              size="large"
              class="upload-btn px-8 py-3 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
            >
              <i class="fa-solid fa-cloud-upload-alt mr-2"></i>
              选择视频文件
            </el-button>
            
            <!-- 支持格式提示 -->
            <div class="format-tips mt-8">
              <div class="flex flex-wrap justify-center gap-3">
                <div class="format-tag bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  MP4
                </div>
                <div class="format-tag bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  MOV
                </div>
                <div class="format-tag bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  AVI
                </div>
                <div class="format-tag bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  MKV
                </div>
                <div class="format-tag bg-red-100 text-red-700 px-3 py-1 rounded-full text-sm font-medium">
                  <i class="fa-solid fa-file-video mr-1"></i>
                  WEBM
                </div>
              </div>
            </div>
            
            <p class="text-xs text-[var(--text-light)] mt-6">
              {{ $t('videoUpload.uploadLimit') }}
            </p>
          </div>
        </div>

        <!-- 最近使用项目展示 -->
        <div class="recent-projects mt-16">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-[var(--text-dark)]">
              <i class="fa-solid fa-clock-rotate-left text-[var(--primary)] mr-2"></i>
              最近使用
            </h3>
            <el-button
              text
              type="primary"
              @click="goToProjectList"
              class="view-all-btn"
              size="small"
            >
              查看全部
              <i class="fa-solid fa-arrow-right ml-1"></i>
            </el-button>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="loading-state text-center py-8">
            <div class="inline-flex items-center space-x-2 text-[var(--text-light)]">
              <div class="w-4 h-4 border-2 border-[var(--primary)]/20 border-t-[var(--primary)] rounded-full animate-spin"></div>
              <span class="text-sm">加载中...</span>
            </div>
          </div>

          <!-- 项目预览卡片网格 -->
          <div v-else-if="recentProjects.length > 0" class="projects-grid grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            <div
              v-for="project in recentProjects"
              :key="project.id"
              class="project-card bg-white rounded-xl shadow-sm hover:shadow-lg border border-gray-100 hover:border-[var(--primary)]/30 transition-all duration-300 cursor-pointer group overflow-hidden"
              @click="openProject(project)"
            >
              <!-- 项目预览缩略图 -->
              <div class="project-thumbnail relative aspect-video bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
                <!-- 如果有缩略图就显示 -->
                <img
                  v-if="project.thumbnail"
                  :src="project.thumbnail"
                  :alt="project.name"
                  class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  @error="handleImageError"
                />
                <!-- 没有缩略图时的占位符 -->
                <div v-else class="w-full h-full flex items-center justify-center">
                  <div class="text-center">
                    <div class="w-12 h-12 bg-[var(--primary)]/10 rounded-full flex items-center justify-center mx-auto mb-2">
                      <i class="fa-solid fa-video text-[var(--primary)] text-xl"></i>
                    </div>
                    <span class="text-xs text-gray-400 font-medium">视频项目</span>
                  </div>
                </div>

                <!-- 悬停播放按钮 -->
                <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <div class="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center shadow-lg">
                    <i class="fa-solid fa-play text-[var(--primary)] ml-0.5"></i>
                  </div>
                </div>

                <!-- AI标识 -->
                <div class="absolute top-2 left-2">
                  <div class="bg-[var(--primary)]/90 text-white text-xs px-2 py-0.5 rounded-full font-medium">
                    AI
                  </div>
                </div>
              </div>

              <!-- 项目信息 -->
              <div class="project-info p-3">
                <h4 class="project-name font-medium text-[var(--text-dark)] text-sm truncate mb-1 group-hover:text-[var(--primary)] transition-colors">
                  {{ project.name }}
                </h4>
                <p class="project-time text-xs text-[var(--text-light)]">
                  {{ formatRelativeTime(project.updateTime) }}
                </p>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state text-center py-12">
            <div class="w-20 h-20 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-6">
              <i class="fa-solid fa-folder-open text-3xl text-gray-400"></i>
            </div>
            <h4 class="text-lg font-semibold text-[var(--text-dark)] mb-3">还没有项目</h4>
            <p class="text-[var(--text-light)] mb-6">上传您的第一个视频开始创作吧！</p>
            <el-button
              type="primary"
              @click="handleFileSelect"
              size="large"
              class="create-first-btn"
            >
              <i class="fa-solid fa-plus mr-2"></i>
              创建第一个项目
            </el-button>
          </div>
        </div>
      </div>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-100 py-8 mt-16">
      <div class="max-w-4xl mx-auto px-4 text-center text-sm text-[var(--text-light)]">
        {{ $t('videoUpload.footerText') }}
      </div>
    </footer>

    <!-- 🎯 处理完成界面 -->
    <div v-if="completionState.isCompleted" class="completion-overlay fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div class="completion-modal bg-white rounded-2xl p-8 max-w-lg w-full mx-4 shadow-2xl">
        <div class="text-center">
          <!-- 完成图标 -->
          <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fa-solid fa-check text-3xl text-green-600"></i>
          </div>

          <!-- 完成标题 -->
          <h3 class="text-2xl font-bold text-[var(--text-dark)] mb-4">
            视频处理完成！
          </h3>

          <!-- 处理结果摘要 -->
          <div v-if="processingResult" class="result-summary bg-gray-50 rounded-lg p-4 mb-6 text-left">
            <div class="grid grid-cols-2 gap-4 text-sm mb-4">
              <div>
                <span class="text-[var(--text-light)]">视频时长：</span>
                <span class="font-medium">{{ Math.round(processingResult.totalTime || 0) }}秒</span>
              </div>
              <div>
                <span class="text-[var(--text-light)]">智能分段：</span>
                <span class="font-medium">{{ getSegmentCount() }}个</span>
              </div>
              <div>
                <span class="text-[var(--text-light)]">音频识别：</span>
                <span class="font-medium">{{ processingResult.audioResult ? '已完成' : '未启用' }}</span>
              </div>
              <div>
                <span class="text-[var(--text-light)]">任务ID：</span>
                <span class="font-medium text-xs">{{ processingResult.taskId?.substring(0, 8) }}...</span>
              </div>
            </div>

            <!-- 🎯 分段时间轴可视化 -->
            <div class="segment-timeline">
              <!-- 有分段数据时显示时间轴 -->
              <div v-if="getSegmentBoundaries().length > 1">
                <h4 class="text-sm font-medium text-[var(--text-dark)] mb-3 flex items-center">
                  <i class="fa-solid fa-scissors mr-2 text-[var(--primary)]"></i>
                  智能分段预览
                </h4>

                <!-- 时间轴容器 -->
                <div class="timeline-container bg-white rounded-lg p-3 border">
                  <!-- 时间标尺 -->
                  <div class="time-ruler flex justify-between text-xs text-gray-500 mb-2">
                    <span>0:00</span>
                    <span>{{ formatTime((processingResult.totalTime || 0) / 2) }}</span>
                    <span>{{ formatTime(processingResult.totalTime || 0) }}</span>
                  </div>

                  <!-- 分段轨道 -->
                  <div class="segment-track relative h-8 bg-gray-100 rounded overflow-hidden">
                    <!-- 分段块 -->
                    <div
                      v-for="(segment, index) in getSegmentBlocks()"
                      :key="index"
                      class="segment-block absolute h-full flex items-center justify-center text-xs font-medium text-white cursor-pointer transition-all duration-200 hover:brightness-110"
                      :style="{
                        left: segment.leftPercent + '%',
                        width: segment.widthPercent + '%',
                        backgroundColor: getSegmentColor(index)
                      }"
                      :title="`片段 ${index + 1}: ${formatTime(segment.startTime)} - ${formatTime(segment.endTime)} (时长: ${formatTime(segment.duration)})`"
                    >
                      {{ index + 1 }}
                    </div>

                    <!-- 空状态提示 -->
                    <div v-if="getSegmentBlocks().length === 0" class="absolute inset-0 flex items-center justify-center text-xs text-gray-500">
                      <i class="fa-solid fa-exclamation-triangle mr-1"></i>
                      分段数据加载中...
                    </div>
                  </div>

                  <!-- 分段统计 -->
                  <div class="segment-stats flex justify-between items-center mt-3 text-xs text-gray-600">
                    <span>共 {{ getSegmentCount() }} 个智能分段</span>
                    <span v-if="getAverageSegmentDuration() > 0">
                      平均时长: {{ formatTime(getAverageSegmentDuration()) }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 无分段数据时的提示 -->
              <div v-else-if="processingResult.totalTime > 0" class="no-segments-notice bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div class="flex items-center text-blue-700">
                  <i class="fa-solid fa-info-circle mr-2"></i>
                  <div>
                    <div class="font-medium text-sm">未检测到智能分段</div>
                    <div class="text-xs text-blue-600 mt-1">
                      视频将作为单个完整片段处理 (时长: {{ formatTime(processingResult.totalTime) }})
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 用户选择 -->
          <div class="choice-section mb-6">
            <h4 class="text-lg font-semibold text-[var(--text-dark)] mb-4">选择下一步操作</h4>
            <div class="choice-buttons grid grid-cols-2 gap-3">
              <button
                @click="handleUserChoice('jump')"
                class="choice-btn p-4 border-2 border-gray-200 rounded-lg hover:border-[var(--primary)] transition-colors"
                :class="{ 'border-[var(--primary)] bg-[var(--primary)]/5': completionState.userChoice === 'jump' }"
              >
                <div class="choice-icon text-2xl mb-2">🎬</div>
                <div class="choice-label font-medium text-sm">进入剪辑页面</div>
                <div class="choice-desc text-xs text-[var(--text-light)]">开始编辑视频</div>
              </button>

              <button
                @click="handleUserChoice('stay')"
                class="choice-btn p-4 border-2 border-gray-200 rounded-lg hover:border-[var(--primary)] transition-colors"
                :class="{ 'border-[var(--primary)] bg-[var(--primary)]/5': completionState.userChoice === 'stay' }"
              >
                <div class="choice-icon text-2xl mb-2">📊</div>
                <div class="choice-label font-medium text-sm">查看详细结果</div>
                <div class="choice-desc text-xs text-[var(--text-light)]">分析处理数据</div>
              </button>
            </div>
          </div>

          <!-- 自动跳转倒计时 -->
          <div v-if="completionState.autoJumpEnabled && completionState.jumpCountdown > 0" class="auto-jump-notice bg-blue-50 rounded-lg p-4 mb-6">
            <div class="countdown-content flex items-center justify-between">
              <span class="countdown-text text-sm text-blue-700">
                {{ completionState.jumpCountdown }}秒后自动进入剪辑页面
              </span>
              <button @click="cancelAutoJump" class="cancel-btn text-xs text-blue-600 hover:text-blue-800 underline">
                取消自动跳转
              </button>
            </div>
            <div class="countdown-progress mt-2 w-full bg-blue-200 rounded-full h-1">
              <div
                class="countdown-fill bg-blue-600 h-1 rounded-full transition-all duration-1000"
                :style="{ width: `${(10 - completionState.jumpCountdown) * 10}%` }"
              ></div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons flex gap-3">
            <button @click="resetUpload" class="btn-outline flex-1 py-3 px-4 border border-gray-300 rounded-lg text-[var(--text-dark)] hover:bg-gray-50 transition-colors">
              上传新视频
            </button>

            <button
              v-if="completionState.userChoice === 'jump'"
              @click="navigateToEditor(processingResult)"
              class="btn-primary flex-1 py-3 px-4 bg-[var(--primary)] text-white rounded-lg hover:bg-[var(--primary)]/90 transition-colors"
            >
              进入剪辑页面
            </button>

            <button
              v-if="completionState.userChoice === 'stay'"
              @click="viewDetailedResults"
              class="btn-primary flex-1 py-3 px-4 bg-[var(--primary)] text-white rounded-lg hover:bg-[var(--primary)]/90 transition-colors"
            >
              查看详细结果
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件处理状态覆盖层 -->
    <div v-if="isProcessing" class="processing-overlay fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div class="processing-modal bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
        <div class="text-center">
          <!-- 处理图标 -->
          <div class="w-16 h-16 bg-[var(--primary)]/10 rounded-full flex items-center justify-center mx-auto mb-6">
            <div class="w-8 h-8 border-4 border-[var(--primary)]/20 border-t-[var(--primary)] rounded-full animate-spin"></div>
          </div>

          <!-- 处理状态 -->
          <div v-if="processingStatus">
            <h3 class="text-xl font-semibold text-[var(--text-dark)] mb-2">
              {{ processingStatus.title }}
            </h3>
            <p class="text-[var(--text-light)] mb-6">
              {{ processingStatus.description }}
            </p>
          </div>

          <!-- 🎯 三阶段进度指示器 -->
          <div class="stage-indicators mb-6">
            <div class="flex items-center justify-between relative">
              <!-- 连接线 -->
              <div class="absolute top-4 left-8 right-8 h-0.5 bg-gray-200">
                <div
                  class="h-full bg-[var(--primary)] transition-all duration-500"
                  :style="{ width: getStageConnectionProgress() + '%' }"
                ></div>
              </div>

              <!-- 阶段1: 视频处理 -->
              <div class="stage-item flex flex-col items-center relative z-10">
                <div
                  class="w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-300"
                  :class="getStageClasses('video_processing')"
                >
                  <div v-if="stageProgress.stage === 'video_processing'" class="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <i v-else-if="isStageCompleted('video_processing')" class="fa-solid fa-check text-xs text-white"></i>
                  <i v-else class="fa-solid fa-video text-xs"></i>
                </div>
                <span class="text-xs mt-2 font-medium" :class="getStageTextClasses('video_processing')">视频处理</span>
              </div>

              <!-- 阶段2: AI识别 -->
              <div class="stage-item flex flex-col items-center relative z-10">
                <div
                  class="w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-300"
                  :class="getStageClasses('ai_recognition')"
                >
                  <div v-if="stageProgress.stage === 'ai_recognition'" class="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <i v-else-if="isStageCompleted('ai_recognition')" class="fa-solid fa-check text-xs text-white"></i>
                  <i v-else class="fa-solid fa-brain text-xs"></i>
                </div>
                <span class="text-xs mt-2 font-medium" :class="getStageTextClasses('ai_recognition')">AI识别</span>
              </div>

              <!-- 阶段3: 处理完成 -->
              <div class="stage-item flex flex-col items-center relative z-10">
                <div
                  class="w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-300"
                  :class="getStageClasses('upload')"
                >
                  <div v-if="stageProgress.stage === 'upload'" class="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <i v-else-if="isStageCompleted('upload')" class="fa-solid fa-check text-xs text-white"></i>
                  <i v-else class="fa-solid fa-cloud-upload text-xs"></i>
                </div>
                <span class="text-xs mt-2 font-medium" :class="getStageTextClasses('upload')">处理完成</span>
              </div>
            </div>
          </div>

          <!-- 总体进度条 -->
          <div class="progress-container mb-4">
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm font-medium text-[var(--text-dark)]">总体进度</span>
              <span class="text-sm font-semibold text-[var(--primary)]">{{ stageProgress.overallProgress }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
              <div
                class="bg-gradient-to-r from-[var(--primary)] to-blue-500 h-3 rounded-full transition-all duration-500"
                :style="{ width: stageProgress.overallProgress + '%' }"
              ></div>
            </div>
          </div>

          <!-- 当前阶段进度条 -->
          <div class="stage-progress-container mb-4">
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm font-medium text-[var(--text-dark)]">当前阶段</span>
              <span class="text-sm font-semibold text-green-600">{{ stageProgress.stageProgress }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="bg-gradient-to-r from-green-500 to-green-400 h-2 rounded-full transition-all duration-300"
                :style="{ width: stageProgress.stageProgress + '%' }"
              ></div>
            </div>
          </div>

          <!-- 提示信息和预计时间 -->
          <div class="processing-tips text-center">
            <p class="text-xs text-[var(--text-light)] mb-2">
              请勿关闭页面，正在处理您的视频...
            </p>

            <!-- 预计处理时间提示 -->
            <div v-if="selectedFile" class="estimated-time text-xs text-gray-500">
              <i class="fa-solid fa-clock mr-1"></i>
              预计处理时间: {{ getEstimatedProcessingTime() }}
            </div>

            <!-- 处理阶段提示 -->
            <div class="stage-tips mt-2 text-xs text-gray-600">
              <div v-if="stageProgress.stage === 'video_processing'">
                <i class="fa-solid fa-video mr-1"></i>
                正在解码视频帧，提取特征...
              </div>
              <div v-else-if="stageProgress.stage === 'ai_recognition'">
                <i class="fa-solid fa-brain mr-1"></i>
                AI正在分析视频内容，识别场景变化...
              </div>
              <div v-else-if="stageProgress.stage === 'upload'">
                <i class="fa-solid fa-check mr-1"></i>
                处理即将完成，正在整理结果...
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import {
  Upload,
  Warning,
  InfoFilled
} from '@element-plus/icons-vue'
import { db, type Project } from '@/db/db'
import { getFileUrl } from '@/utils/opfs-file'
import { write } from 'opfs-tools'
import { type Track, type TrackClip } from '@/types/track'
import dayjs from 'dayjs'

// 导入组件
import JijianHeader from '@/components/jijian-ui/JijianHeader.vue'
import JijianLoginModal from '@/components/jijian-ui/JijianLoginModal.vue'

// 组合式API
const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()

// 响应式数据
const showLoginModal = ref(false)
const showRegisterModal = ref(false)
const isDragging = ref(false)
const webCodecsSupported = ref(true)

// 项目相关数据
const loading = ref(false)
const recentProjects = ref<Project[]>([])
const maxRecentProjects = 5 // 最多显示5个最近项目

// 文件上传相关数据
const selectedFile = ref<File | null>(null)
const isProcessing = ref(false)
const processingProgress = ref(0)
const processingStatus = ref<{title: string, description: string} | null>(null)

// 视频处理相关数据
const currentTaskId = ref<string | null>(null)
const processingResult = ref<any>(null)
const showProcessingWarning = ref(false)
const isPageUnloadBlocked = ref(false)

// 三阶段进度数据
const stageProgress = ref({
  stage: 'video_processing',
  stageName: '视频处理中',
  stageProgress: 0,
  overallProgress: 0,
  description: '准备开始处理'
})

// 处理选项
const processingOptions = ref({
  autoSegment: true,
  extractAudio: true,
  generateThumbnails: true,
  featureExtractor: 'tensorflow',
  targetFps: '5',
  algorithmType: 'go'
})

// 🎯 处理完成状态管理
const completionState = ref({
  isCompleted: false,
  showResults: false,
  autoJumpEnabled: true,
  jumpCountdown: 0,
  resultPreview: null,
  userChoice: null as 'jump' | 'stay' | null
})

// 进度平滑器类
class ProgressSmoother {
  private currentProgress = 0
  private targetProgress = 0
  private speed = 0.02
  private animationId: number | null = null
  private updateCallback: (progress: number) => void

  constructor(updateCallback: (progress: number) => void) {
    this.updateCallback = updateCallback
  }

  setTarget(target: number) {
    this.targetProgress = Math.max(0, Math.min(100, target))
    if (this.animationId === null) {
      this.animate()
    }
  }

  private animate() {
    const diff = this.targetProgress - this.currentProgress

    if (Math.abs(diff) < 0.1) {
      this.currentProgress = this.targetProgress
      this.updateCallback(this.currentProgress)
      this.animationId = null
      return
    }

    this.currentProgress += diff * this.speed
    this.updateCallback(this.currentProgress)
    this.animationId = requestAnimationFrame(() => this.animate())
  }

  setSpeed(speed: number) {
    this.speed = speed
  }

  destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }
}

// 智能进度计算器
class SmartProgressCalculator {
  private stages = {
    video_processing: { weight: 0.7, name: '视频处理中' },
    ai_recognition: { weight: 0.2, name: 'AI识别视频中' },
    upload: { weight: 0.1, name: '上传处理中' }
  } as const

  private currentStage: keyof typeof this.stages = 'video_processing'
  private stageProgress = 0

  setStage(stage: keyof typeof this.stages, progress: number) {
    this.currentStage = stage
    this.stageProgress = Math.max(0, Math.min(100, progress))
  }

  getOverallProgress(): number {
    let overallProgress = 0
    const stages = Object.keys(this.stages) as Array<keyof typeof this.stages>

    for (const stage of stages) {
      if (stage === this.currentStage) {
        overallProgress += (this.stageProgress / 100) * this.stages[stage].weight * 100
        break
      } else {
        overallProgress += this.stages[stage].weight * 100
      }
    }

    return Math.min(100, overallProgress)
  }

  getStageName(): string {
    return this.stages[this.currentStage].name
  }
}

// 创建进度平滑器
const progressSmoother = new ProgressSmoother((progress) => {
  processingProgress.value = Math.round(progress)
})

const stageProgressSmoother = new ProgressSmoother((progress) => {
  stageProgress.value.stageProgress = Math.round(progress)
})

const overallProgressSmoother = new ProgressSmoother((progress) => {
  stageProgress.value.overallProgress = Math.round(progress)
})

// 方法
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
}

const handleDragLeave = () => {
  isDragging.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = false
  const files = e.dataTransfer?.files
  if (files && files.length > 0) {
    handleFiles(files)
  }
}

const handleFileSelect = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'video/*'
  input.multiple = true
  input.onchange = (e) => {
    const target = e.target as HTMLInputElement
    if (target.files) {
      handleFiles(target.files)
    }
  }
  input.click()
}

const handleFiles = async (files: FileList) => {
  if (files.length === 0) return

  const file = files[0]

  // 检查文件类型
  if (!file.type.startsWith('video/')) {
    ElMessage.error('请选择视频文件')
    return
  }

  // 检查文件大小 (限制为500MB)
  const maxSize = 500 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过500MB')
    return
  }

  selectedFile.value = file
  console.log('选择的文件:', file.name, '大小:', formatFileSize(file.size))

  // 开始处理文件
  await processVideoFile(file)
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 处理视频文件
const processVideoFile = async (file: File) => {
  try {
    isProcessing.value = true
    processingProgress.value = 0
    showProcessingWarning.value = true
    isPageUnloadBlocked.value = true

    // 添加页面离开确认
    window.addEventListener('beforeunload', handleBeforeUnload)

    // 步骤1: 保存文件到OPFS
    processingStatus.value = {
      title: '保存文件',
      description: '正在将文件保存到本地存储...'
    }

    await saveFileToOPFS(file)

    // 步骤2: 开始智能处理
    if (webCodecsSupported.value && processingOptions.value.autoSegment) {
      // 使用 WebCodecs 进行智能处理
      await startWebCodecsProcessing(file)
    } else {
      // 使用基础处理模式
      await startBasicProcessing(file)
    }

  } catch (error) {
    console.error('处理视频文件失败:', error)
    ElMessage.error('处理失败: ' + (error instanceof Error ? error.message : '未知错误'))

    // 移除处理警告和页面离开阻止
    showProcessingWarning.value = false
    isPageUnloadBlocked.value = false
    window.removeEventListener('beforeunload', handleBeforeUnload)
  } finally {
    isProcessing.value = false
  }
}

// 页面离开确认处理
const handleBeforeUnload = (event: BeforeUnloadEvent) => {
  if (isPageUnloadBlocked.value) {
    event.preventDefault()
    return '视频正在处理中，离开页面将中断处理。确定要离开吗？'
  }
}

// 检查视频兼容性
const checkVideoCompatibility = async () => {
  try {
    processingStatus.value = {
      title: '检查视频兼容性',
      description: '正在检查浏览器对视频格式的支持...'
    }

    // 检查 WebCodecs 支持
    if (!('VideoDecoder' in window) || !('VideoEncoder' in window)) {
      throw new Error('浏览器不支持 WebCodecs API')
    }

    // 检查基本的视频格式支持
    const video = document.createElement('video')
    const canPlayH264 = video.canPlayType('video/mp4; codecs="avc1.42E01E"')
    const canPlayWebM = video.canPlayType('video/webm; codecs="vp8"')

    if (!canPlayH264 && !canPlayWebM) {
      console.warn('⚠️ 浏览器对常见视频格式支持有限')
    }

    console.log('✅ 视频兼容性检查通过')
  } catch (error) {
    console.warn('⚠️ 视频兼容性检查失败:', error)
    // 不抛出错误，继续处理
  }
}

// WebCodecs 智能处理（严格按照index.html的逻辑）
const startWebCodecsProcessing = async (file: File) => {
  try {
    // 🎯 严格按照index.html的逻辑：处理前必须有任务ID
    console.log('📝 开始处理前先创建任务...')
    await createTask()

    if (!currentTaskId.value) {
      throw new Error('任务创建失败，无法继续处理')
    }

    console.log('✅ 任务创建成功，任务ID:', currentTaskId.value)

    // 检查视频格式兼容性
    processingStatus.value = {
      title: '检查视频格式',
      description: '正在分析视频编码格式...'
    }

    await checkVideoCompatibility()

    // 导入必要的模块
    const { VideoProcessingCoordinator } = await import('@/utils/webcodecs-decoder/src/coordinator.js')

    processingStatus.value = {
      title: '初始化协调器...',
      description: '正在设置视频处理协调器'
    }

    // 创建协调器（完全按照 index.html 的实现）
    const coordinator = new VideoProcessingCoordinator({
      onProgress: (processedFrames: number, totalFrames: number) => {
        // 🎯 使用智能进度计算器
        const stageProgressValue = totalFrames > 0 ? (processedFrames / totalFrames) * 100 : 0
        const smartCalc = new SmartProgressCalculator()
        smartCalc.setStage('video_processing', stageProgressValue)

        const overallProgressValue = smartCalc.getOverallProgress()

        // 🎯 使用平滑器更新进度，设置更快的速度
        progressSmoother.setSpeed(0.05) // 视频处理阶段更快
        stageProgressSmoother.setTarget(stageProgressValue)
        overallProgressSmoother.setTarget(overallProgressValue)
        progressSmoother.setTarget(overallProgressValue)

        // 更新阶段信息
        stageProgress.value.stage = 'video_processing'
        stageProgress.value.stageName = smartCalc.getStageName()
        stageProgress.value.description = `正在处理视频帧 ${processedFrames}/${totalFrames}`

        processingStatus.value = {
          title: smartCalc.getStageName(),
          description: `正在处理视频帧 ${processedFrames}/${totalFrames}`
        }
      },
      onComplete: (stats: any) => {
        processingProgress.value = 100
        stageProgress.value = {
          stage: 'uploading',
          stageName: '处理完成',
          stageProgress: 100,
          overallProgress: 100,
          description: '视频处理和上传已完成'
        }

        processingStatus.value = {
          title: '处理完成',
          description: '智能分段和音频识别已完成'
        }

        // 保存处理结果
        processingResult.value = {
          totalTime: stats.totalTime,
          segmentCount: stats.segmentTimes?.length || 0,
          audioResult: stats.asrResult,
          fusionResult: stats.fusionResult,
          taskId: currentTaskId.value
        }

        // 移除处理警告和页面离开阻止
        showProcessingWarning.value = false
        isPageUnloadBlocked.value = false
        window.removeEventListener('beforeunload', handleBeforeUnload)
        isProcessing.value = false

        // 🎯 处理完成，显示完成界面
        console.log('✅ 视频处理完成，显示完成界面')

        // 设置完成状态
        completionState.value.isCompleted = true
        completionState.value.resultPreview = stats

        // 启动自动跳转倒计时
        startAutoJumpCountdown()

        // 更新处理状态
        processingStatus.value = {
          title: '处理完成',
          description: '智能分段和音频识别已完成'
        }
      },
      onError: (error: Error) => {
        console.error('❌ 处理错误:', error)

        // 🎯 检查是否是视频兼容性问题 - 这些错误应该由coordinator处理降级
        if (error.message.includes('视频文件格式不兼容') ||
            error.message.includes('H.265') ||
            error.message.includes('findPosition') ||
            error.message.includes('MP4Box') ||
            error.message.includes('视频track数据不完整') ||
            error.message.includes('stsc表') ||
            error.message.includes('chunk偏移信息')) {

          // 显示兼容性警告，但让coordinator处理降级逻辑
          ElMessage.warning({
            message: '检测到视频兼容性问题，正在尝试音频分段模式...',
            duration: 5000
          })

          // 更新处理状态，等待coordinator的降级处理
          processingStatus.value = {
            title: 'AI识别视频中',
            description: '视频解码遇到兼容性问题，正在切换到音频分段模式...'
          }

          // 切换到AI识别阶段
          const smartCalc = new SmartProgressCalculator()
          smartCalc.setStage('ai_recognition', 20)
          const overallProgressValue = smartCalc.getOverallProgress()

          progressSmoother.setSpeed(0.03)
          progressSmoother.setTarget(overallProgressValue)

          stageProgress.value.stage = 'ai_recognition'
          stageProgress.value.stageName = smartCalc.getStageName()
          stageProgress.value.description = '正在切换到音频分段模式...'

          console.log('⚠️ 视频兼容性问题，等待coordinator降级处理')
          // 不抛出错误，让coordinator的降级逻辑处理
          return
        }

        // 只有真正的错误才中断处理
        showProcessingWarning.value = false
        isPageUnloadBlocked.value = false
        window.removeEventListener('beforeunload', handleBeforeUnload)
        isProcessing.value = false

        ElMessage.error('处理失败: ' + error.message)
        // 对于非兼容性错误，仍然抛出
        // throw error // 注释掉，让coordinator决定是否继续
      },
      onAudioProgress: (status: string, progressPercent?: number) => {
        // 🎯 使用智能进度计算器
        let stageProgressValue = 0
        if (typeof progressPercent === 'number') {
          stageProgressValue = progressPercent
        } else {
          // 基于状态文本智能估算进度
          if (status.includes('开始') || status.includes('初始化')) {
            stageProgressValue = 15
          } else if (status.includes('加载') || status.includes('准备')) {
            stageProgressValue = 30
          } else if (status.includes('分析') || status.includes('识别')) {
            stageProgressValue = 60
          } else if (status.includes('处理') || status.includes('转换')) {
            stageProgressValue = 80
          } else if (status.includes('完成') || status.includes('结束')) {
            stageProgressValue = 95
          } else {
            stageProgressValue = 45
          }
        }

        const smartCalc = new SmartProgressCalculator()
        smartCalc.setStage('ai_recognition', stageProgressValue)
        const overallProgressValue = smartCalc.getOverallProgress()

        // 🎯 使用平滑器更新进度，AI阶段使用中等速度
        progressSmoother.setSpeed(0.03)
        stageProgressSmoother.setTarget(stageProgressValue)
        overallProgressSmoother.setTarget(overallProgressValue)
        progressSmoother.setTarget(overallProgressValue)

        // 更新阶段信息
        stageProgress.value.stage = 'ai_recognition'
        stageProgress.value.stageName = smartCalc.getStageName()
        stageProgress.value.description = status

        processingStatus.value = {
          title: smartCalc.getStageName(),
          description: status
        }
      },
      onResume: (pausedState: any) => {
        console.log('🔄 收到重新处理请求:', pausedState)
        // 可以在这里添加重新处理的逻辑
      },
      onAudioComplete: (result: any) => {
        console.log('✅ 音频处理完成:', result)
        const sentenceCount = result.sentences?.length || 0
        console.log(`🎵 音频识别完成！识别到 ${sentenceCount} 个句子`)

        // 显示详细的音频识别结果
        if (result.sentences && result.sentences.length > 0) {
          result.sentences.forEach((sentence: any, index: number) => {
            console.log(`句子 ${index + 1}: [${sentence.startTime.toFixed(2)}s - ${sentence.endTime.toFixed(2)}s] ${sentence.content}`)
          })
        }
      },
      onAudioError: (error: Error) => {
        console.error('❌ 音频处理失败:', error)
        ElMessage.warning(`音频识别失败: ${error.message}，视频处理将继续进行`)
      },
      onChapterProgress: (status: string) => {
        console.log('📚 章节聚合进度:', status)
        // 可以在这里更新章节处理进度
      },
      onChapterComplete: (result: any) => {
        console.log('✅ 章节聚合完成:', result)
        console.log(`📚 章节聚合完成！状态: ${result.status}`)
      },
      onChapterError: (error: Error) => {
        console.error('❌ 章节聚合失败:', error)
        console.log(`章节聚合失败: ${error.message}，但视频处理已完成`)
      }
    })

    // 初始化特征提取器（完全按照 index.html 的逻辑）
    try {
      const featureExtractorType = processingOptions.value.featureExtractor
      const extractorType = featureExtractorType.startsWith('tensorflow') ? 'tensorflow' : featureExtractorType
      const useBatch = featureExtractorType === 'tensorflow' // 批处理模式
      const batchSize = 8

      await coordinator.initializeFeatureExtractor(extractorType as any, useBatch, batchSize)

      let extractorName
      if (featureExtractorType === 'tensorflow') {
        extractorName = `TensorFlow.js GPU (批处理, ${batchSize}帧/批)`
      } else if (featureExtractorType === 'tensorflow-single') {
        extractorName = 'TensorFlow.js GPU (单帧)'
      } else {
        extractorName = 'OpenCV.js CPU'
      }

      console.log(`🎯 ${extractorName} 特征提取器已启用`)
    } catch (error) {
      console.warn('特征提取启用失败:', error)
      console.log('⚠️ 特征提取不可用，继续普通解码')
    }

    // 设置算法配置（参考 index.html）
    const algorithmConfig = {
      min_avg_duration: 8,
      max_avg_duration: 50,
      initial_pen: 5,
      max_iterations: 20,
      min_size: 10
    }
    coordinator.setAlgorithmConfig(algorithmConfig)
    coordinator.setAlgorithmType(processingOptions.value.algorithmType as any)

    // 设置融合配置
    const fusionConfig = {
      minSegmentDuration: 1.0,
      dialogueProtectionMargin: 0.5
    }
    coordinator.setFusionConfig(fusionConfig)

    // 开始处理
    const targetFps = processingOptions.value.targetFps ? parseInt(processingOptions.value.targetFps) : undefined
    await coordinator.startProcessing(file, currentTaskId.value, targetFps)

  } catch (error) {
    console.error('WebCodecs 处理失败:', error)
    throw error
  }
}

// 基础处理模式
const startBasicProcessing = async (file: File) => {
  try {
    // 使用集成工具
    const { createBasicVideoProcessor } = await import('@/utils/webcodecs-integration')

    const processor = createBasicVideoProcessor({
      onProgress: (processedFrames: number, totalFrames: number) => {
        const progress = totalFrames > 0 ? Math.round((processedFrames / totalFrames) * 100) : 0
        processingProgress.value = progress
      },
      onStageProgress: (progress: any) => {
        stageProgress.value = {
          stage: progress.stage,
          stageName: progress.stageName,
          stageProgress: progress.stageProgress,
          overallProgress: progress.overallProgress,
          description: progress.description
        }

        processingStatus.value = {
          title: progress.stageName,
          description: progress.description
        }
      },
      onComplete: async (stats: any) => {
        processingProgress.value = 100
        processingStatus.value = {
          title: '处理完成',
          description: '基础处理已完成'
        }

        // 保存处理结果
        processingResult.value = {
          totalTime: stats.totalTime,
          segmentCount: stats.segmentTimes?.length || 0,
          audioResult: stats.asrResult,
          taskId: generateProjectId()
        }

        // 处理完成，不需要立即跳转，由用户选择
      },
      onError: (error: Error) => {
        throw error
      }
    })

    // 开始处理
    await processor.processVideo(file, {
      autoSegment: processingOptions.value.autoSegment,
      extractAudio: processingOptions.value.extractAudio,
      generateThumbnails: processingOptions.value.generateThumbnails,
      featureExtractor: processingOptions.value.featureExtractor as any,
      targetFps: processingOptions.value.targetFps,
      algorithmType: processingOptions.value.algorithmType as any
    })

  } catch (error) {
    console.error('基础处理失败:', error)
    throw error
  }
}

// 创建任务（严格按照index.html的实现）
const createTask = async () => {
  try {
    console.log('🚀 开始创建任务...')

    // 🎯 使用与index.html完全相同的方式：导入预创建的taskManager实例
    const { taskManager } = await import('@/utils/webcodecs-decoder/src/task-manager')

    console.log('📝 正在创建视频处理任务...')

    // 🎯 使用与index.html完全相同的方法名
    const taskResponse = await taskManager.createVideoProcessingTask()

    currentTaskId.value = taskResponse.task_id

    console.log('✅ 任务创建成功:', taskResponse)
    console.log('📋 任务ID:', currentTaskId.value)

  } catch (error) {
    console.error('❌ 任务创建失败:', error)

    // 🎯 如果远程任务创建失败，使用本地模式（与index.html的降级策略一致）
    console.log('📝 降级到本地任务模式')
    currentTaskId.value = generateProjectId()
    console.log('📝 使用本地生成的任务ID:', currentTaskId.value)
  }
}



// 保存文件到OPFS
const saveFileToOPFS = async (file: File) => {
  try {
    const filePath = `/video/${file.name}`
    console.log('💾 开始保存文件到 OPFS:', filePath)

    // 使用 opfs-tools 写入文件
    await write(filePath, file.stream())

    console.log('✅ 文件已保存到 OPFS:', filePath)
  } catch (error) {
    console.error('❌ 保存文件到 OPFS 失败:', error)
    throw new Error('文件保存失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 创建视频项目
const createVideoProject = async (file: File, stats?: any): Promise<string> => {
  try {
    const projectId = generateProjectId()
    const now = Date.now()

    // 从处理结果中获取视频时长
    const videoDuration = stats?.totalTime || 0

    // 创建项目数据
    const project: Project = {
      id: projectId,
      name: file.name.replace(/\.[^/.]+$/, ''), // 移除文件扩展名
      createTime: now,
      updateTime: now,
      tracks: [
        {
          id: 'video-track-1',
          clips: [
            {
              id: 'clip-1',
              name: file.name,
              startTime: 0,
              duration: videoDuration,
              endTime: videoDuration,
              path: `/video/${file.name}`,
              type: 'video'
            }
          ]
        }
      ]
    }

    // 保存到数据库
    await db.projects.add(project)

    console.log('✅ 项目创建成功:', projectId)

    // 刷新项目列表
    await loadRecentProjects()

    return projectId
  } catch (error) {
    console.error('❌ 创建项目失败:', error)
    throw new Error('项目创建失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 生成项目ID
const generateProjectId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

// 🎯 自动跳转倒计时
let countdownTimer: number | null = null

const startAutoJumpCountdown = () => {
  if (!completionState.value.autoJumpEnabled) return

  completionState.value.jumpCountdown = 10 // 10秒倒计时

  countdownTimer = setInterval(() => {
    if (completionState.value.jumpCountdown > 0) {
      completionState.value.jumpCountdown--
    } else {
      // 倒计时结束，自动跳转
      clearInterval(countdownTimer!)
      countdownTimer = null

      if (completionState.value.autoJumpEnabled && processingResult.value) {
        console.log('⏰ 自动跳转倒计时结束，跳转到剪辑页面')
        navigateToEditor(processingResult.value)
      }
    }
  }, 1000)

  console.log('⏰ 启动自动跳转倒计时: 10秒')
}

// 🎯 用户选择处理
const handleUserChoice = (choice: 'jump' | 'stay') => {
  completionState.value.userChoice = choice

  if (choice === 'jump') {
    // 取消自动跳转倒计时
    completionState.value.autoJumpEnabled = false
    completionState.value.jumpCountdown = 0
  }

  console.log('👤 用户选择:', choice)
}

// 🎯 取消自动跳转
const cancelAutoJump = () => {
  completionState.value.autoJumpEnabled = false
  completionState.value.jumpCountdown = 0
  console.log('⏹️ 用户取消自动跳转')
}

// 🎯 重置上传
const resetUpload = () => {
  // 重置所有状态
  selectedFile.value = null
  isProcessing.value = false
  processingProgress.value = 0
  processingResult.value = null
  completionState.value = {
    isCompleted: false,
    showResults: false,
    autoJumpEnabled: true,
    jumpCountdown: 0,
    resultPreview: null,
    userChoice: null
  }
  stageProgress.value = {
    stage: 'video_processing',
    stageName: '视频处理中',
    stageProgress: 0,
    overallProgress: 0,
    description: '准备开始处理'
  }
  processingStatus.value = null
  currentTaskId.value = null

  // 清除倒计时
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }

  console.log('🔄 上传状态已重置')
}

// 🎯 查看详细结果
const viewDetailedResults = () => {
  completionState.value.showResults = true
  console.log('📊 显示详细结果')
}

// 🎯 跳转到编辑器
const navigateToEditor = async (result: any) => {
  try {
    // 🎯 构建剪辑器需要的完整数据
    const editorData = {
      // 视频文件信息
      videoFile: {
        name: selectedFile.value?.name,
        size: selectedFile.value?.size,
        type: selectedFile.value?.type,
        duration: result.totalTime || 0,
        // 🎯 添加完整的文件路径信息
        originalPath: selectedFile.value?.name,
        filePath: `/video/${selectedFile.value?.name}`,
        // 🎯 添加文件验证信息
        lastModified: selectedFile.value?.lastModified,
        webkitRelativePath: selectedFile.value?.webkitRelativePath || ''
      },

      // 处理结果
      processingResult: {
        totalTime: result.totalTime,
        totalFrames: result.totalFrames,
        videoDuration: result.totalTime || 0,
        segmentTimes: result.segmentTimes || [],
        fusionResult: result.fusionResult,
        asrResult: result.asrResult,
        segmentMetadata: result.segmentMetadata,
        taskId: currentTaskId.value,
        // 🎯 添加详细的处理状态信息
        processingComplete: true,
        processingTimestamp: new Date().toISOString(),
        processingDuration: 0, // 处理时长（暂时设为0）
        // 🎯 添加分段统计信息
        segmentStats: {
          totalSegments: result.fusionResult?.adjustedBoundaries ? (result.fusionResult.adjustedBoundaries.length - 1) : 0,
          hasIntelligentSegments: !!(result.fusionResult?.adjustedBoundaries && result.fusionResult.adjustedBoundaries.length > 1),
          hasAudioRecognition: !!(result.asrResult?.sentences?.length),
          audioSentenceCount: result.asrResult?.sentences?.length || 0
        }
      },

      // 🎯 转换分段数据为track格式
      segmentData: convertSegmentsToTrackData(result, selectedFile.value!),

      // 🎯 增强的元数据
      metadata: {
        uploadTimestamp: new Date().toISOString(),
        processingMethod: webCodecsSupported.value ? 'webcodecs' : 'basic',
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        // 🎯 添加处理环境信息
        environment: {
          webCodecsSupported: webCodecsSupported.value,
          wasmSupported: typeof WebAssembly !== 'undefined',
          opfsSupported: 'storage' in navigator && 'getDirectory' in navigator.storage
        }
      }
    }

    sessionStorage.setItem('videoEditorData', JSON.stringify(editorData))
    console.log('💾 剪辑器数据已保存到 sessionStorage')

    // 创建项目
    const projectId = await createVideoProject(selectedFile.value!, result)

    // 跳转到编辑页面
    await router.push(`/clip/${projectId}?filename=${encodeURIComponent(selectedFile.value?.name || 'unknown')}`)

  } catch (error) {
    console.error('❌ 跳转到剪辑页面失败:', error)
    ElMessage.error('跳转失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}



const handleLoginSuccess = (data: any) => {
  userStore.setUserInfo(data.user)
  userStore.setToken(data.token)
  userStore.setLoginStatus(true)
  ElMessage.success('登录成功！')
}

const handleRegisterSuccess = (data: any) => {
  userStore.setUserInfo(data.user)
  userStore.setLoginStatus(true)
  ElMessage.success('注册成功！')
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人资料页面
      break
    case 'settings':
      // 跳转到设置页面
      break
    case 'logout':
      userStore.logout()
      ElMessage.success('已退出登录')
      break
  }
}

// 项目相关方法 - 支持缩略图预览
const loadRecentProjects = async () => {
  try {
    loading.value = true
    // 获取基本项目信息
    const allProjects = await db.projects
      .orderBy('updateTime')
      .reverse()
      .limit(maxRecentProjects)
      .toArray()

    // 异步加载缩略图，不阻塞界面显示
    recentProjects.value = allProjects

    // 后台加载缩略图
    loadProjectThumbnails(allProjects)
  } catch (error) {
    console.error('Failed to load recent projects:', error)
    ElMessage.error('加载项目失败')
  } finally {
    loading.value = false
  }
}

// 异步加载项目缩略图
const loadProjectThumbnails = async (projects: Project[]) => {
  for (const project of projects) {
    if (project.thumbnail) {
      try {
        const thumbnailUrl = await getFileUrl(project.thumbnail)
        // 更新对应项目的缩略图
        const index = recentProjects.value.findIndex(p => p.id === project.id)
        if (index !== -1) {
          recentProjects.value[index].thumbnail = thumbnailUrl
        }
      } catch (error) {
        console.warn('Failed to load thumbnail for project:', project.id, error)
        // 缩略图加载失败时保持为undefined，会显示占位符
      }
    }
  }
}



// 轻量级时间格式化 - 相对时间显示
const formatRelativeTime = (timestamp?: number) => {
  if (!timestamp) return '未知时间'

  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  // 超过一周显示具体日期
  return dayjs(timestamp).format('MM-DD')
}

const openProject = (project: Project) => {
  if (!project.id) {
    ElMessage.error('项目ID无效')
    return
  }

  try {
    // 跳转到编辑页面
    router.push(`/clip/${project.id}`)
  } catch (error) {
    console.error('Failed to navigate to project:', error)
    ElMessage.error('打开项目失败')
  }
}

const goToProjectList = () => {
  try {
    // 跳转到项目列表页面
    router.push('/projects')
  } catch (error) {
    console.error('Failed to navigate to project list:', error)
    ElMessage.error('跳转失败')
  }
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 图片加载失败时隐藏图片，显示占位符
  img.style.display = 'none'
}

// 🎯 阶段进度相关方法
// 获取阶段连接线进度
const getStageConnectionProgress = (): number => {
  const currentStage = stageProgress.value.stage
  const overallProgress = stageProgress.value.overallProgress

  if (currentStage === 'video_processing') {
    return Math.min(50, overallProgress * 0.7) // 视频处理阶段最多到50%
  } else if (currentStage === 'ai_recognition') {
    return Math.min(100, 50 + (overallProgress - 70) * 2.5) // AI识别阶段从50%到100%
  } else if (currentStage === 'upload') {
    return 100 // 完成阶段，连接线100%
  }

  return 0
}

// 判断阶段是否已完成
const isStageCompleted = (stage: string): boolean => {
  const currentStage = stageProgress.value.stage
  const overallProgress = stageProgress.value.overallProgress

  if (stage === 'video_processing') {
    return overallProgress > 70 || currentStage === 'ai_recognition' || currentStage === 'upload'
  } else if (stage === 'ai_recognition') {
    return overallProgress > 90 || currentStage === 'upload'
  } else if (stage === 'upload') {
    return overallProgress >= 100
  }

  return false
}

// 获取阶段样式类
const getStageClasses = (stage: string): string => {
  const currentStage = stageProgress.value.stage
  const isCompleted = isStageCompleted(stage)
  const isActive = currentStage === stage

  if (isCompleted) {
    return 'bg-[var(--primary)] border-[var(--primary)] text-white'
  } else if (isActive) {
    return 'bg-[var(--primary)] border-[var(--primary)] text-white'
  } else {
    return 'bg-white border-gray-300 text-gray-400'
  }
}

// 获取阶段文本样式类
const getStageTextClasses = (stage: string): string => {
  const currentStage = stageProgress.value.stage
  const isCompleted = isStageCompleted(stage)
  const isActive = currentStage === stage

  if (isCompleted || isActive) {
    return 'text-[var(--primary)]'
  } else {
    return 'text-gray-400'
  }
}

// 🎯 分段数据处理相关方法
// 获取分段边界数组
const getSegmentBoundaries = (): number[] => {
  try {
    if (!processingResult.value) {
      console.warn('⚠️ 处理结果为空，返回默认边界')
      return []
    }

    // 优先使用fusionResult的adjustedBoundaries
    if (processingResult.value.fusionResult?.adjustedBoundaries &&
        Array.isArray(processingResult.value.fusionResult.adjustedBoundaries) &&
        processingResult.value.fusionResult.adjustedBoundaries.length >= 2) {
      console.log('✅ 使用fusionResult.adjustedBoundaries:', processingResult.value.fusionResult.adjustedBoundaries)
      return processingResult.value.fusionResult.adjustedBoundaries
    }

    // 备用：使用segmentTimes
    if (processingResult.value.segmentTimes &&
        Array.isArray(processingResult.value.segmentTimes) &&
        processingResult.value.segmentTimes.length >= 2) {
      console.log('✅ 使用segmentTimes:', processingResult.value.segmentTimes)
      return processingResult.value.segmentTimes
    }

    // 如果都没有，返回开始和结束时间
    const totalTime = processingResult.value.totalTime || 0
    if (totalTime > 0) {
      console.log('⚠️ 没有分段数据，使用完整视频时长:', [0, totalTime])
      return [0, totalTime]
    }

    console.warn('⚠️ 无法获取有效的时间信息')
    return []

  } catch (error) {
    console.error('❌ 获取分段边界失败:', error)
    return []
  }
}

// 获取分段数量
const getSegmentCount = (): number => {
  const boundaries = getSegmentBoundaries()
  return Math.max(0, boundaries.length - 1)
}

// 格式化时间显示
const formatTime = (seconds: number): string => {
  if (!seconds || seconds < 0) return '0:00'

  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)

  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 获取分段块数据（用于时间轴渲染）
const getSegmentBlocks = () => {
  const boundaries = getSegmentBoundaries()
  const totalTime = processingResult.value?.totalTime || 0

  if (boundaries.length < 2 || totalTime <= 0) return []

  const blocks = []

  for (let i = 0; i < boundaries.length - 1; i++) {
    const startTime = boundaries[i]
    const endTime = boundaries[i + 1]
    const duration = endTime - startTime

    const leftPercent = (startTime / totalTime) * 100
    const widthPercent = (duration / totalTime) * 100

    blocks.push({
      index: i,
      startTime,
      endTime,
      duration,
      leftPercent: Math.max(0, leftPercent),
      widthPercent: Math.max(0.5, widthPercent) // 最小宽度0.5%确保可见
    })
  }

  return blocks
}

// 获取分段颜色
const getSegmentColor = (index: number): string => {
  const colors = [
    '#4f46e5', // 紫色
    '#06b6d4', // 青色
    '#10b981', // 绿色
    '#f59e0b', // 橙色
    '#ef4444', // 红色
    '#8b5cf6', // 紫罗兰
    '#14b8a6', // 蓝绿色
    '#f97316', // 橙红色
  ]

  return colors[index % colors.length]
}

// 获取平均分段时长
const getAverageSegmentDuration = (): number => {
  const boundaries = getSegmentBoundaries()
  const segmentCount = getSegmentCount()

  if (segmentCount === 0) return 0

  let totalDuration = 0
  for (let i = 0; i < boundaries.length - 1; i++) {
    totalDuration += boundaries[i + 1] - boundaries[i]
  }

  return totalDuration / segmentCount
}

// 🎯 预计处理时间估算
const getEstimatedProcessingTime = (): string => {
  if (!selectedFile.value) return '未知'

  try {
    const fileSizeGB = selectedFile.value.size / (1024 * 1024 * 1024)
    const baseTimePerGB = webCodecsSupported.value ? 30 : 60 // WebCodecs更快

    // 根据文件大小估算处理时间
    let estimatedSeconds = Math.ceil(fileSizeGB * baseTimePerGB)

    // 最小30秒，最大10分钟
    estimatedSeconds = Math.max(30, Math.min(600, estimatedSeconds))

    if (estimatedSeconds < 60) {
      return `约 ${estimatedSeconds} 秒`
    } else {
      const minutes = Math.ceil(estimatedSeconds / 60)
      return `约 ${minutes} 分钟`
    }
  } catch (error) {
    console.warn('⚠️ 预计时间计算失败:', error)
    return '约 1-3 分钟'
  }
}

// 🎯 将分段数据转换为track数据结构
const convertSegmentsToTrackData = (result: any, file: File) => {
  try {
    console.log('🔄 转换分段数据为track数据结构:', result)

    const boundaries = getSegmentBoundaries()
    const segmentCount = getSegmentCount()

    if (segmentCount === 0) {
      console.warn('⚠️ 没有分段数据，创建单个完整片段')
      return {
        segments: [{
          id: generateProjectId(),
          name: file.name,
          startTime: 0,
          endTime: result.totalTime || 60,
          duration: result.totalTime || 60
        }],
        totalDuration: result.totalTime || 60
      }
    }

    // 创建分段数据
    const segments = []
    for (let i = 0; i < boundaries.length - 1; i++) {
      const startTime = boundaries[i]
      const endTime = boundaries[i + 1]
      const duration = endTime - startTime

      segments.push({
        id: generateProjectId(),
        name: `片段 ${i + 1}`,
        startTime: startTime,
        endTime: endTime,
        duration: duration,
        // 添加额外的元数据
        segmentIndex: i,
        hasAudio: !!(result.asrResult?.sentences?.some((s: any) =>
          s.startTime >= startTime && s.endTime <= endTime
        )),
        color: getSegmentColor(i)
      })
    }

    console.log('✅ 分段数据转换完成:', segments)

    return {
      segments,
      totalDuration: result.totalTime || 0,
      segmentMetadata: {
        totalSegments: segmentCount,
        averageDuration: getAverageSegmentDuration(),
        hasIntelligentSegments: segmentCount > 1,
        hasAudioRecognition: !!(result.asrResult?.sentences?.length),
        processingMethod: 'webcodecs',
        fusionResult: result.fusionResult
      }
    }

  } catch (error) {
    console.error('❌ 转换分段数据失败:', error)
    return {
      segments: [],
      totalDuration: 0,
      segmentMetadata: null
    }
  }
}



// 生命周期
onMounted(() => {
  // 检查WebCodecs支持
  webCodecsSupported.value = 'VideoDecoder' in window && 'VideoEncoder' in window

  // 自动加载最近的项目
  loadRecentProjects()
})
</script>

<style scoped>
/* 上传页面样式 */
.jijian-upload-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 上传区域样式 */
.upload-zone {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.upload-zone:hover {
  transform: translateY(-4px);
}

.upload-zone:hover .upload-icon-container {
  transform: scale(1.05);
}

.upload-zone.dragging {
  transform: scale(1.02) translateY(-4px);
  border-color: var(--primary);
  background: rgba(139, 92, 246, 0.05);
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
}

/* 上传按钮样式 */
.upload-btn {
  background: linear-gradient(135deg, var(--primary) 0%, #7c3aed 100%);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-btn:hover {
  background: linear-gradient(135deg, #5b21b6 0%, #6d28d9 100%);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

/* 格式标签样式 */
.format-tag {
  transition: all 0.3s ease;
  cursor: pointer;
}

.format-tag:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 项目预览卡片样式 */
.project-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.project-thumbnail {
  position: relative;
  overflow: hidden;
}

.project-thumbnail img {
  transition: transform 0.3s ease;
}

.project-card:hover .project-thumbnail img {
  transform: scale(1.05);
}

/* 默认提示样式 */
.default-hint {
  border: 1px dashed #e5e7eb;
  transition: all 0.3s ease;
}

.default-hint:hover {
  border-color: var(--primary);
  background-color: #fafafa;
}

/* 加载状态 */
.loading-state {
  opacity: 0.8;
}

/* 空状态样式 */
.empty-state {
  opacity: 0.9;
}

.create-first-btn {
  background: linear-gradient(135deg, var(--primary) 0%, #7c3aed 100%);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.create-first-btn:hover {
  background: linear-gradient(135deg, #5b21b6 0%, #6d28d9 100%);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
  transform: translateY(-2px);
}

/* 按钮样式优化 */
.load-projects-btn {
  transition: all 0.2s ease;
}

.load-projects-btn:hover {
  transform: translateY(-1px);
}

.view-all-btn {
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  transform: translateX(2px);
}

/* 动画效果 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.animate-bounce {
  animation: bounce 2s infinite;
}

/* 处理警告样式 */
.processing-warning {
  border-left: 4px solid #f59e0b;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

/* 🎯 分段时间轴样式 */
.segment-timeline {
  margin-top: 16px;
}

.timeline-container {
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.segment-track {
  position: relative;
  background: linear-gradient(90deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 1px solid #d1d5db;
}

.segment-block {
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
  font-size: 10px;
  font-weight: 600;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);

  &:hover {
    transform: translateY(-1px);
    box-shadow:
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:last-child {
    border-right: none;
  }
}

.segment-stats {
  padding-top: 8px;
  border-top: 1px solid #e5e7eb;
}

/* 阶段指示器样式增强 */
.stage-indicators {
  .stage-item {
    min-width: 60px;
  }

  .stage-item span {
    white-space: nowrap;
    text-align: center;
  }
}

/* 🎯 处理提示样式增强 */
.processing-tips {
  .estimated-time {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 6px;
    padding: 4px 8px;
    display: inline-block;
    margin: 0 auto;
  }

  .stage-tips {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: 6px;
    padding: 6px 12px;
    margin: 8px auto 0;
    max-width: 280px;
  }
}

/* 无分段数据提示样式 */
.no-segments-notice {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #93c5fd;

  .fa-info-circle {
    color: #2563eb;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-zone {
    padding: 2rem;
  }

  .format-tips {
    margin-top: 1.5rem;
  }

  .recent-projects h3 {
    font-size: 1.25rem;
  }

  .projects-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .project-card {
    margin-bottom: 0.5rem;
  }

  .project-info {
    padding: 0.5rem;
  }

  .default-hint {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .project-card {
    max-width: 280px;
    margin: 0 auto 0.75rem;
  }
}
</style>
