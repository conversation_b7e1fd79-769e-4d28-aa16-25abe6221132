<template>
  <div class="upload-page">
    <!-- 顶部导航 -->
    <div class="upload-header">
      <div class="header-content">
        <h1 class="page-title">
          <svg class="w-8 h-8 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
          </svg>
          智能视频处理
        </h1>
        <p class="page-subtitle">选择您要编辑的视频文件，我们将为您智能分析和处理</p>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="upload-content">
      <div class="upload-container">

        <!-- 处理警告提示 -->
        <div v-if="showProcessingWarning" class="processing-warning">
          <div class="warning-icon">
            <svg class="w-6 h-6 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
          </div>
          <div class="warning-content">
            <h3 class="warning-title">正在处理视频</h3>
            <p class="warning-description">
              请勿关闭或切换页面，否则处理将中断。处理完成前请保持页面打开。
            </p>
          </div>
        </div>

        <!-- 🎯 任务管理区域已隐藏，任务会在上传时自动创建 -->
        <!-- WebCodecs 支持检查 -->
        <div v-if="!webCodecsSupported" class="support-warning">
          <div class="warning-icon">
            <svg class="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
          </div>
          <div class="warning-content">
            <h3 class="warning-title">浏览器兼容性提示</h3>
            <p class="warning-description">
              当前浏览器不支持 WebCodecs API，将使用基础处理模式。
              <br>推荐使用 Chrome 94+ 或 Edge 94+ 以获得最佳体验。
            </p>
          </div>
        </div>

        <!-- 文件上传区域 -->
        <div
          class="upload-zone"
          :class="{
            'drag-over': isDragOver,
            'has-file': selectedFile,
            'processing': isProcessing
          }"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @dragleave="handleDragLeave"
          @click="triggerFileSelect"
        >
          <input
            ref="fileInput"
            type="file"
            accept="video/*"
            @change="handleFileSelect"
            class="hidden"
          />

          <!-- 默认状态 -->
          <div v-if="!selectedFile && !isProcessing" class="upload-prompt">
            <div class="upload-icon">
              <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
              </svg>
            </div>
            <h3 class="upload-title">拖拽视频文件到这里</h3>
            <p class="upload-description">或者 <span class="text-blue-500 font-medium">点击选择文件</span></p>
            <div class="upload-formats">
              <span class="format-tag">MP4</span>
              <span class="format-tag">MOV</span>
              <span class="format-tag">AVI</span>
              <span class="format-tag">MKV</span>
            </div>
          </div>

          <!-- 文件选中状态 -->
          <div v-else-if="selectedFile && !isProcessing" class="file-selected">
            <div class="file-icon">
              <svg class="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
              </svg>
            </div>
            <div class="file-info">
              <h4 class="file-name">{{ selectedFile.name }}</h4>
              <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
            </div>
            <button @click.stop="removeFile" class="remove-file-btn">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>

          <!-- 处理中状态 -->
          <div v-else-if="isProcessing" class="processing-state">
            <div class="processing-spinner">
              <svg class="animate-spin w-12 h-12 text-blue-500" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>

            <!-- 三阶段进度显示 -->
            <div class="stage-info">
              <h4 class="processing-title">{{ stageProgress.stageName }}</h4>
              <p class="processing-description">{{ stageProgress.description }}</p>

              <!-- 阶段指示器 -->
              <div class="stage-indicators">
                <div class="stage-indicator" :class="{ active: stageProgress.stage === 'video_processing' || stageProgress.overallProgress > 0 }">
                  <div class="stage-dot"></div>
                  <span class="stage-label">视频处理</span>
                </div>
                <div class="stage-connector" :class="{ active: stageProgress.overallProgress > 70 }"></div>
                <div class="stage-indicator" :class="{ active: stageProgress.stage === 'ai_recognition' || stageProgress.overallProgress > 70 }">
                  <div class="stage-dot"></div>
                  <span class="stage-label">AI识别</span>
                </div>
                <div class="stage-connector" :class="{ active: stageProgress.overallProgress > 90 }"></div>
                <div class="stage-indicator" :class="{ active: stageProgress.stage === 'uploading' || stageProgress.overallProgress > 90 }">
                  <div class="stage-dot"></div>
                  <span class="stage-label">上传</span>
                </div>
              </div>
            </div>

            <!-- 总体进度条 -->
            <div class="processing-progress">
              <div class="progress-info">
                <span class="progress-label">总体进度</span>
                <span class="progress-text">{{ stageProgress.overallProgress }}%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: stageProgress.overallProgress + '%' }"></div>
              </div>
            </div>

            <!-- 当前阶段进度条 -->
            <div class="stage-progress">
              <div class="progress-info">
                <span class="progress-label">当前阶段</span>
                <span class="progress-text">{{ stageProgress.stageProgress }}%</span>
              </div>
              <div class="progress-bar stage-progress-bar">
                <div class="progress-fill stage-progress-fill" :style="{ width: stageProgress.stageProgress + '%' }"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 智能处理选项 -->
        <div v-if="selectedFile && !isProcessing" class="processing-options">
          <h3 class="options-title">智能处理选项</h3>

          <!-- 基础处理选项 -->
          <div class="option-section">
            <h4 class="section-title">基础功能</h4>

            <div class="option-group">
              <label class="option-label">
                <input
                  type="checkbox"
                  v-model="processingOptions.autoSegment"
                  class="option-checkbox"
                />
                <span class="option-text">智能分段</span>
                <span class="option-description">使用 AI 算法自动识别场景变化并分段</span>
              </label>
            </div>

            <div class="option-group">
              <label class="option-label">
                <input
                  type="checkbox"
                  v-model="processingOptions.extractAudio"
                  class="option-checkbox"
                />
                <span class="option-text">音频识别</span>
                <span class="option-description">提取音频并进行语音识别，优化分段边界</span>
              </label>
            </div>

            <div class="option-group">
              <label class="option-label">
                <input
                  type="checkbox"
                  v-model="processingOptions.generateThumbnails"
                  class="option-checkbox"
                />
                <span class="option-text">生成缩略图</span>
                <span class="option-description">为每个片段生成预览图</span>
              </label>
            </div>
          </div>

          <!-- 高级处理选项 -->
          <div v-if="webCodecsSupported" class="option-section">
            <h4 class="section-title">高级设置</h4>

            <div class="option-row">
              <label class="option-label-inline">
                <span class="option-text">特征提取器:</span>
                <select v-model="processingOptions.featureExtractor" class="option-select">
                  <option value="tensorflow">TensorFlow.js (GPU批处理)</option>
                  <option value="tensorflow-single">TensorFlow.js (GPU单帧)</option>
                  <option value="opencv">OpenCV.js (CPU)</option>
                </select>
              </label>
            </div>

            <div class="option-row">
              <label class="option-label-inline">
                <span class="option-text">目标帧率:</span>
                <select v-model="processingOptions.targetFps" class="option-select">
                  <option value="">原始帧率</option>
                  <option value="5">5 FPS</option>
                  <option value="10">10 FPS</option>
                  <option value="15">15 FPS</option>
                  <option value="24">24 FPS</option>
                </select>
              </label>
            </div>

            <div class="option-row">
              <label class="option-label-inline">
                <span class="option-text">算法类型:</span>
                <select v-model="processingOptions.algorithmType" class="option-select">
                  <option value="go">Go WASM (推荐)</option>
                  <option value="python">Python WASM</option>
                </select>
              </label>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div v-if="selectedFile && !isProcessing" class="upload-actions">
          <button @click="removeFile" class="cancel-btn">
            重新选择
          </button>
          <button @click="startProcessing" class="upload-btn" :disabled="!canStartProcessing">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
            </svg>
            {{ getProcessingButtonText() }}
          </button>
        </div>

        <!-- 🎯 优化后的处理完成界面 -->
        <div v-if="completionState.isCompleted" class="completion-container">
          <!-- 完成状态头部 -->
          <div class="completion-header">
            <div class="completion-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" fill="#10b981"/>
                <path d="M9 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="completion-content">
              <h3 class="completion-title">视频处理完成</h3>
              <p class="completion-description">智能分段和音频识别已完成，您可以选择下一步操作</p>
            </div>
          </div>

          <!-- 处理结果预览 -->
          <div v-if="processingResult" class="result-preview">
            <div class="preview-stats">
              <div class="stat-item">
                <div class="stat-value">{{ formatDuration(processingResult.totalTime) }}</div>
                <div class="stat-label">处理时长</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ processingResult.segmentCount }}</div>
                <div class="stat-label">智能分段</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ processingResult.audioResult?.sentences?.length || 0 }}</div>
                <div class="stat-label">音频句子</div>
              </div>
            </div>
          </div>

          <!-- 用户选择区域 -->
          <div class="completion-actions">
            <div class="action-choice">
              <h4 class="choice-title">选择下一步操作</h4>
              <div class="choice-buttons">
                <button
                  @click="handleUserChoice('jump')"
                  class="choice-btn primary"
                  :class="{ active: completionState.userChoice === 'jump' }"
                >
                  <div class="choice-icon">🎬</div>
                  <div class="choice-content">
                    <div class="choice-label">进入剪辑页面</div>
                    <div class="choice-desc">开始编辑视频</div>
                  </div>
                </button>

                <button
                  @click="handleUserChoice('stay')"
                  class="choice-btn secondary"
                  :class="{ active: completionState.userChoice === 'stay' }"
                >
                  <div class="choice-icon">📊</div>
                  <div class="choice-content">
                    <div class="choice-label">查看详细结果</div>
                    <div class="choice-desc">分析处理数据</div>
                  </div>
                </button>
              </div>
            </div>

            <!-- 自动跳转倒计时 -->
            <div v-if="completionState.autoJumpEnabled && completionState.jumpCountdown > 0" class="auto-jump-notice">
              <div class="countdown-content">
                <span class="countdown-text">{{ completionState.jumpCountdown }}秒后自动进入剪辑页面</span>
                <button @click="cancelAutoJump" class="cancel-btn">取消自动跳转</button>
              </div>
              <div class="countdown-progress">
                <div
                  class="countdown-fill"
                  :style="{ width: `${(10 - completionState.jumpCountdown) * 10}%` }"
                ></div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <button @click="resetUpload" class="btn-outline">
                上传新视频
              </button>

              <button
                v-if="completionState.userChoice === 'jump'"
                @click="navigateToEditor(processingResult)"
                class="btn-primary"
              >
                进入剪辑页面
              </button>

              <button
                v-if="completionState.userChoice === 'stay'"
                @click="viewDetailedResults"
                class="btn-primary"
              >
                查看详细结果
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { write } from 'opfs-tools';

// 路由
const router = useRouter();

// 响应式数据
const fileInput = ref<HTMLInputElement>();
const selectedFile = ref<File | null>(null);
const isDragOver = ref(false);
const isProcessing = ref(false);
const processingProgress = ref(0);
const webCodecsSupported = ref(false);
const originalFileDuration = ref<number>(0); // 🎯 存储原始文件时长

// 处理状态
const processingStatus = ref({
  title: '正在初始化...',
  description: '准备开始处理'
});

// 三阶段进度状态
const stageProgress = ref({
  stage: 'video_processing',
  stageName: '视频处理中',
  stageProgress: 0,
  overallProgress: 0,
  description: '准备开始处理'
});

// 🎯 进度平滑控制器
class ProgressSmoother {
  private targetProgress = 0;
  private currentProgress = 0;
  private animationId: number | null = null;
  private updateCallback: (progress: number) => void;
  private speed = 0.02; // 动画速度

  constructor(updateCallback: (progress: number) => void) {
    this.updateCallback = updateCallback;
  }

  setTarget(target: number) {
    this.targetProgress = Math.max(0, Math.min(100, target));
    if (!this.animationId) {
      this.animate();
    }
  }

  private animate() {
    const diff = this.targetProgress - this.currentProgress;

    if (Math.abs(diff) < 0.1) {
      this.currentProgress = this.targetProgress;
      this.updateCallback(this.currentProgress);
      this.animationId = null;
      return;
    }

    this.currentProgress += diff * this.speed;
    this.updateCallback(this.currentProgress);
    this.animationId = requestAnimationFrame(() => this.animate());
  }

  setSpeed(speed: number) {
    this.speed = speed;
  }

  destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }
}

// 创建进度平滑器
const progressSmoother = new ProgressSmoother((progress) => {
  processingProgress.value = Math.round(progress);
});

const stageProgressSmoother = new ProgressSmoother((progress) => {
  stageProgress.value.stageProgress = Math.round(progress);
});

const overallProgressSmoother = new ProgressSmoother((progress) => {
  stageProgress.value.overallProgress = Math.round(progress);
});

// 🎯 智能进度计算器
class SmartProgressCalculator {
  private stages = {
    video_processing: { weight: 0.7, name: '视频处理中' },
    ai_recognition: { weight: 0.2, name: 'AI识别视频中' },
    upload: { weight: 0.1, name: '上传处理中' }
  } as const;

  private currentStage: keyof typeof this.stages = 'video_processing';
  private stageProgress = 0;

  setStage(stage: keyof typeof this.stages, progress: number) {
    this.currentStage = stage;
    this.stageProgress = Math.max(0, Math.min(100, progress));
  }

  getOverallProgress(): number {
    let totalProgress = 0;

    // 计算已完成阶段的进度
    const stageKeys = ['video_processing', 'ai_recognition', 'upload'] as const;
    const currentStageIndex = stageKeys.indexOf(this.currentStage);

    for (let i = 0; i < currentStageIndex; i++) {
      const stage = stageKeys[i];
      totalProgress += this.stages[stage].weight * 100;
    }

    // 加上当前阶段的进度
    if (currentStageIndex >= 0) {
      const currentStageWeight = this.stages[this.currentStage].weight;
      totalProgress += (this.stageProgress / 100) * currentStageWeight * 100;
    }

    return Math.round(totalProgress);
  }

  getStageName(): string {
    return this.stages[this.currentStage]?.name || '处理中';
  }

  getStageProgress(): number {
    return this.stageProgress;
  }
}

// 用户体验增强
const showProcessingWarning = ref(false);
const isPageUnloadBlocked = ref(false);

// 处理结果
const processingResult = ref<any>(null);

// 🎯 处理完成状态管理
const completionState = ref({
  isCompleted: false,
  showResults: false,
  autoJumpEnabled: true,
  jumpCountdown: 0,
  resultPreview: null,
  userChoice: null // 'jump' | 'stay' | null
});

// 处理选项
const processingOptions = ref({
  autoSegment: true,
  extractAudio: true,
  generateThumbnails: true,
  featureExtractor: 'tensorflow',
  targetFps: '5',
  algorithmType: 'go'
});

// 计算属性
const canStartProcessing = computed(() => {
  if (!selectedFile.value || isProcessing.value) return false;

  // 🎯 移除对任务创建的依赖，文件上传时会自动创建任务
  return true;
});

// 文件拖拽处理
const handleDragOver = (e: DragEvent) => {
  e.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault();
  isDragOver.value = false;
};

const handleDrop = async (e: DragEvent) => {
  e.preventDefault();
  isDragOver.value = false;

  const files = e.dataTransfer?.files;
  if (files && files.length > 0) {
    const file = files[0];
    if (file.type.startsWith('video/')) {
      selectedFile.value = file;

      // 🎯 立即将文件保存到 OPFS
      await saveFileToOPFS(file);
    } else {
      alert('请选择视频文件');
    }
  }
};

// 任务管理
const currentTaskId = ref<string | null>(null);
const taskInfo = ref<string>('等待文件上传');

// 本地任务管理器（降级方案）
const localTaskManager = {
  tasks: new Map(),

  createTask(request: any) {
    const taskId = `local_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const task = {
      task_id: taskId,
      name: request.name,
      description: request.description,
      status: 'created',
      created_at: new Date().toISOString()
    };

    this.tasks.set(taskId, task);
    console.log('📝 本地任务创建:', task);
    return Promise.resolve(task);
  },

  deleteTask(taskId: string) {
    const deleted = this.tasks.delete(taskId);
    console.log('🗑️ 本地任务删除:', { taskId, deleted });
    return Promise.resolve();
  },

  getTask(taskId: string) {
    return this.tasks.get(taskId);
  }
};



// 创建任务
const createTask = async () => {
  try {
    console.log('🚀 开始创建任务...');

    // 显示当前环境变量配置
    console.log('🔧 当前环境变量:', {
      VITE_TASK_BACKEND_URL: import.meta.env.VITE_TASK_BACKEND_URL,
      VITE_TASK_BACKEND_ENABLED: import.meta.env.VITE_TASK_BACKEND_ENABLED,
      MODE: import.meta.env.MODE
    });

    // 检查环境变量配置
    const { getTaskBackendConfig } = await import('@/config/backend');
    const { backendEnabled } = getTaskBackendConfig();

    console.log('🔍 检查后端服务配置:', { backendEnabled });

    let taskResponse: any;
    let shouldTryRemote = backendEnabled;

    // 如果用户明确启用了后端，直接尝试，不依赖健康检查
    if (shouldTryRemote) {
      console.log('🌐 用户启用了后端服务，直接尝试远程任务创建');
    } else {
      console.log('📝 后端服务未启用，使用本地模式');
    }

    if (shouldTryRemote) {
      console.log('✅ 后端服务可用，使用远程任务管理');
      try {
        const { getTaskBackendConfig } = await import('@/config/backend');
        const { backendUrl } = getTaskBackendConfig();
        const { TaskManager } = await import('@/utils/webcodecs-decoder/src/task-manager');
        const taskManager = new TaskManager(backendUrl);

        taskResponse = await taskManager.createTask({
          name: `视频处理任务_${new Date().toLocaleString()}`,
          description: '智能视频分段处理'
        });

        console.log('✅ 远程任务创建成功:', taskResponse);
      } catch (remoteError) {
        console.warn('⚠️ 远程任务创建失败，降级到本地模式:', remoteError);
        taskResponse = await localTaskManager.createTask({
          name: `视频处理任务_${new Date().toLocaleString()}`,
          description: '智能视频分段处理'
        });
      }
    } else {
      console.log('📝 使用本地任务管理');
      taskResponse = await localTaskManager.createTask({
        name: `视频处理任务_${new Date().toLocaleString()}`,
        description: '智能视频分段处理'
      });
    }

    currentTaskId.value = taskResponse.task_id;
    taskInfo.value = `任务ID: ${taskResponse.task_id}`;

    console.log('✅ 任务创建成功:', taskResponse);
  } catch (error) {
    console.error('❌ 任务创建失败:', error);
    taskInfo.value = '任务创建失败: ' + error.message;
  }
};

// 删除任务
const deleteTask = async () => {
  if (!currentTaskId.value) return;

  try {
    // 检查是否是本地任务
    if (currentTaskId.value.startsWith('local_')) {
      console.log('🗑️ 删除本地任务');
      await localTaskManager.deleteTask(currentTaskId.value);
    } else {
      console.log('🗑️ 删除远程任务');
      try {
        const { getTaskBackendConfig } = await import('@/config/backend');
        const { backendUrl } = getTaskBackendConfig();
        const { TaskManager } = await import('@/utils/webcodecs-decoder/src/task-manager');
        const taskManager = new TaskManager(backendUrl);
        await taskManager.deleteTask(currentTaskId.value);
      } catch (remoteError) {
        console.warn('⚠️ 远程任务删除失败:', remoteError);
        // 即使远程删除失败，也继续本地清理
      }
    }

    currentTaskId.value = null;
    taskInfo.value = '等待文件上传';

    console.log('✅ 任务删除成功');
  } catch (error) {
    console.error('❌ 任务删除失败:', error);
  }
};

// WebCodecs 支持检查（使用测试工具）
const checkWebCodecsSupport = async () => {
  try {
    // 使用测试工具进行完整检测
    const { fullWebCodecsCheck } = await import('@/utils/webcodecs-test');
    const result = await fullWebCodecsCheck();

    webCodecsSupported.value = result.overall;

    if (result.overall) {
      console.log('🎉 WebCodecs 检测通过！');

      // 预加载 Go WASM 模块（参考 index.html）
      try {
        console.log('🚀 开始预加载Go WASM模块...');
        const { getGoAlgorithmProcessor } = await import('@/utils/webcodecs-decoder/src/algorithm-wasm-go.js');
        const goProcessor = getGoAlgorithmProcessor();
        await goProcessor.initialize();
        console.log('✅ Go WASM模块预加载完成');
      } catch (error) {
        console.warn('⚠️ Go WASM模块预加载失败:', error);
      }
    } else {
      console.warn('⚠️ WebCodecs 检测失败，将使用基础处理模式');
      if (result.issues.length > 0) {
        console.log('问题列表:');
        result.issues.forEach(issue => console.log(`  • ${issue}`));
      }
    }

  } catch (error) {
    console.error('❌ WebCodecs 检测工具加载失败:', error);
    // 降级到基础检测
    const basicSupported = typeof VideoDecoder !== 'undefined' && typeof VideoFrame !== 'undefined';
    webCodecsSupported.value = basicSupported;

    if (basicSupported) {
      console.log('✅ 基础 WebCodecs API 可用');
    } else {
      console.warn('❌ 基础 WebCodecs API 不可用');
    }
  }
};

// 文件选择处理
const triggerFileSelect = () => {
  if (!isProcessing.value) {
    fileInput.value?.click();
  }
};

// 🎯 保存文件到 OPFS
const saveFileToOPFS = async (file: File) => {
  try {
    const filePath = `/video/${file.name}`;
    console.log('💾 开始保存文件到 OPFS:', filePath);

    // 🎯 验证原始文件的时长
    await verifyOriginalFileDuration(file);

    // 使用 opfs-tools 写入文件
    await write(filePath, file.stream());

    console.log('✅ 文件已保存到 OPFS:', filePath);
  } catch (error) {
    console.error('❌ 保存文件到 OPFS 失败:', error);
    ElMessage.error('文件保存失败: ' + error.message);
  }
};

// 🎯 验证原始文件时长
const verifyOriginalFileDuration = async (file: File) => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    video.preload = 'metadata';

    video.onloadedmetadata = () => {
      console.log('🕐 原始文件时长验证:', {
        fileName: file.name,
        fileSize: file.size,
        videoDuration: video.duration,
        videoWidth: video.videoWidth,
        videoHeight: video.videoHeight
      });

      // 🎯 保存原始文件时长
      originalFileDuration.value = video.duration;

      URL.revokeObjectURL(video.src);
      resolve(video.duration);
    };

    video.onerror = () => {
      console.error('❌ 无法读取视频元数据');
      URL.revokeObjectURL(video.src);
      reject(new Error('无法读取视频元数据'));
    };

    video.src = URL.createObjectURL(file);
  });
};

const handleFileSelect = async (e: Event) => {
  const target = e.target as HTMLInputElement;
  const files = target.files;
  if (files && files.length > 0) {
    selectedFile.value = files[0];

    // 🎯 立即将文件保存到 OPFS
    await saveFileToOPFS(files[0]);
  }
};

// 移除文件
const removeFile = () => {
  selectedFile.value = null;
  processingResult.value = null;
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化时长
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}分${remainingSeconds}秒`;
};

// 🎯 格式化时间点（用于分段预览）
const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

// 获取处理按钮文本
const getProcessingButtonText = (): string => {
  if (!webCodecsSupported.value) {
    return '开始基础处理';
  }

  // 🎯 移除对任务创建的检查，直接显示开始处理
  return '开始智能处理';
};

// 页面离开确认处理
const handleBeforeUnload = (event: BeforeUnloadEvent) => {
  if (isPageUnloadBlocked.value) {
    event.preventDefault();
    // 现代浏览器会显示标准的确认对话框
    return '视频正在处理中，离开页面将中断处理。确定要离开吗？';
  }
};

// 开始智能处理
const startProcessing = async () => {
  if (!selectedFile.value) return;

  // 设置处理状态和用户体验增强
  isProcessing.value = true;
  processingProgress.value = 0;
  processingResult.value = null;
  showProcessingWarning.value = true;
  isPageUnloadBlocked.value = true;

  // 添加页面离开确认
  window.addEventListener('beforeunload', handleBeforeUnload);

  try {
    if (webCodecsSupported.value && processingOptions.value.autoSegment) {
      // 使用 WebCodecs 进行智能处理
      await startWebCodecsProcessing();
    } else {
      // 使用基础处理模式
      await startBasicProcessing();
    }
  } catch (error) {
    console.error('处理失败:', error);

    // 移除处理警告和页面离开阻止
    showProcessingWarning.value = false;
    isPageUnloadBlocked.value = false;
    window.removeEventListener('beforeunload', handleBeforeUnload);

    isProcessing.value = false;

    // 如果是编码格式问题，显示详细的解决方案
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    if (errorMessage.includes('codec') || errorMessage.includes('编码') || errorMessage.includes('description')) {
      await showCodecErrorDialog(errorMessage);
    } else {
      processingStatus.value = {
        title: '处理失败',
        description: errorMessage
      };
    }
  }
};

// 检查视频兼容性
const checkVideoCompatibility = async () => {
  try {
    const { detectVideoFormat } = await import('@/utils/video-format-detector');

    const format = await detectVideoFormat(selectedFile.value!);
    if (format) {
      console.log('🎬 检测到视频格式:', format);

      // 这里可以添加更多的兼容性检查
      // 实际的编码检查会在 WebCodecs 解码时进行
    }
  } catch (error) {
    console.warn('视频格式检测失败:', error);
    // 不阻止处理流程，继续执行
  }
};

// 显示编码错误对话框
const showCodecErrorDialog = async (errorMessage: string) => {
  try {
    // 🎯 优先使用H.265兼容性检查工具
    if (errorMessage.includes('H.265') || errorMessage.includes('HEVC') ||
        errorMessage.includes('hvc1') || errorMessage.includes('hev1')) {

      const { checkH265Compatibility } = await import('@/utils/h265-compatibility-checker');

      // 从错误信息中提取编码格式
      const codecMatch = errorMessage.match(/([hH][vV][cC]1|[hH][eE][vV]1|[hH]\.?265|[hH][eE][vV][cC])/);
      const codec = codecMatch ? codecMatch[0] : 'hvc1';

      const h265Result = await checkH265Compatibility(codec);

      // 构建详细的H.265错误信息
      const detailMessage = [
        `检测到H.265/HEVC格式视频，当前浏览器不支持此格式。`,
        '',
        '🔍 问题分析:',
        ...h265Result.issues,
        '',
        '💡 解决建议:',
        ...h265Result.recommendations.slice(0, 3),
        '',
        '🔧 转换命令示例:',
        h265Result.conversionCommands[0] || 'ffmpeg -i input.mp4 -c:v libx264 -c:a copy output.mp4'
      ].join('\n');

      ElMessageBox.alert(detailMessage, 'H.265格式兼容性问题', {
        confirmButtonText: '我知道了',
        type: 'warning',
        dangerouslyUseHTMLString: false
      });

      return;
    }

    // 🎯 对于其他编码格式，使用原有的检测工具
    const { createUserFriendlyError } = await import('@/utils/video-format-detector');

    // 从错误信息中提取编码格式
    const codecMatch = errorMessage.match(/codec[:\s]+([a-zA-Z0-9\.]+)/);
    const codec = codecMatch ? codecMatch[1] : 'unknown';

    const errorInfo = createUserFriendlyError(codec);

    // 使用 Element Plus 的消息框显示详细信息
    ElMessageBox.alert(
      `${errorInfo.message}\n\n${errorInfo.suggestions.join('\n')}`,
      errorInfo.title,
      {
        confirmButtonText: '我知道了',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    );
  } catch (error) {
    // 如果格式检测工具出错，显示原始错误
    ElMessage.error('处理失败: ' + errorMessage);
  }
};

// 🎯 用户选择处理
const handleUserChoice = (choice: 'jump' | 'stay') => {
  completionState.value.userChoice = choice;

  if (choice === 'jump') {
    // 取消自动跳转倒计时
    completionState.value.autoJumpEnabled = false;
    completionState.value.jumpCountdown = 0;
  }

  console.log('👤 用户选择:', choice);
};

// 🎯 取消自动跳转
const cancelAutoJump = () => {
  completionState.value.autoJumpEnabled = false;
  completionState.value.jumpCountdown = 0;
  console.log('⏹️ 用户取消自动跳转');
};

// 🎯 重置上传
const resetUpload = () => {
  // 重置所有状态
  selectedFile.value = null;
  isProcessing.value = false;
  processingProgress.value = 0;
  processingResult.value = null;
  completionState.value = {
    isCompleted: false,
    showResults: false,
    autoJumpEnabled: true,
    jumpCountdown: 0,
    resultPreview: null,
    userChoice: null
  };

  // 重置进度状态
  stageProgress.value = {
    stage: 'video_processing',
    stageName: '视频处理中',
    stageProgress: 0,
    overallProgress: 0,
    description: '准备开始处理'
  };

  console.log('🔄 上传状态已重置');
};

// 🎯 查看详细结果
const viewDetailedResults = () => {
  completionState.value.showResults = true;
  console.log('📊 显示详细结果');
};

// 🎯 自动跳转倒计时
let countdownTimer: number | null = null;

const startAutoJumpCountdown = () => {
  if (!completionState.value.autoJumpEnabled) return;

  completionState.value.jumpCountdown = 10; // 10秒倒计时

  countdownTimer = setInterval(() => {
    if (completionState.value.jumpCountdown > 0) {
      completionState.value.jumpCountdown--;
    } else {
      // 倒计时结束，自动跳转
      clearInterval(countdownTimer!);
      countdownTimer = null;

      if (completionState.value.autoJumpEnabled && processingResult.value) {
        console.log('⏰ 自动跳转倒计时结束，跳转到剪辑页面');
        navigateToEditor(processingResult.value);
      }
    }
  }, 1000);
};

// 跳转到剪辑页面
const navigateToEditor = async (stats: any) => {
  try {
    console.log('🎬 准备跳转到剪辑页面，处理结果:', stats);

    // 🎯 构建剪辑器需要的完整数据
    const editorData = {
      // 视频文件信息
      videoFile: {
        name: selectedFile.value?.name,
        size: selectedFile.value?.size,
        type: selectedFile.value?.type,
        duration: originalFileDuration.value || stats.videoDuration || stats.totalTime || 0,
        // 🎯 添加完整的文件路径信息
        originalPath: selectedFile.value?.name,
        filePath: `/video/${selectedFile.value?.name}`,
        // 🎯 添加文件验证信息
        lastModified: selectedFile.value?.lastModified,
        webkitRelativePath: selectedFile.value?.webkitRelativePath || ''
      },

      // 处理结果
      processingResult: {
        totalTime: stats.totalTime,
        totalFrames: stats.totalFrames,
        videoDuration: originalFileDuration.value || stats.videoDuration || stats.totalTime || 0,
        segmentTimes: stats.segmentTimes || [],
        fusionResult: stats.fusionResult,
        asrResult: stats.asrResult,
        segmentMetadata: stats.segmentMetadata,
        taskId: currentTaskId.value,
        // 🎯 添加详细的处理状态信息
        processingComplete: true,
        processingTimestamp: new Date().toISOString(),
        processingDuration: 0, // 处理时长（暂时设为0）
        // 🎯 添加分段统计信息
        segmentStats: {
          totalSegments: stats.fusionResult?.adjustedBoundaries ? (stats.fusionResult.adjustedBoundaries.length - 1) : 0,
          hasIntelligentSegments: !!(stats.fusionResult?.adjustedBoundaries && stats.fusionResult.adjustedBoundaries.length > 1),
          hasAudioRecognition: !!(stats.asrResult?.sentences?.length),
          audioSentenceCount: stats.asrResult?.sentences?.length || 0
        }
      },

      // 🎯 增强的元数据
      metadata: {
        uploadTimestamp: new Date().toISOString(),
        processingMethod: webCodecsSupported.value ? 'webcodecs' : 'basic',
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        // 🎯 添加处理环境信息
        environment: {
          webCodecsSupported: webCodecsSupported.value,
          wasmSupported: typeof WebAssembly !== 'undefined',
          opfsSupported: 'storage' in navigator && 'getDirectory' in navigator.storage
        },
        // 🎯 添加质量指标
        qualityMetrics: {
          processingTime: 0, // 处理时间（暂时设为0）
          fileSize: selectedFile.value?.size || 0,
          duration: originalFileDuration.value || stats.videoDuration || stats.totalTime || 0,
          avgProcessingSpeed: 0 // 平均处理速度（暂时设为0）
        }
      },

      // 时间戳
      timestamp: new Date().toISOString()
    };

    // 将数据保存到 sessionStorage，供剪辑页面使用
    sessionStorage.setItem('videoEditorData', JSON.stringify(editorData));

    console.log('💾 剪辑器数据已保存到 sessionStorage');
    console.log('📊 数据联动详情:', {
      fileName: editorData.videoFile.name,
      fileSize: editorData.videoFile.size,
      duration: editorData.videoFile.duration,
      hasSegments: editorData.processingResult.segmentStats.totalSegments > 0,
      segmentCount: editorData.processingResult.segmentStats.totalSegments,
      hasAudio: editorData.processingResult.segmentStats.hasAudioRecognition,
      dataSize: JSON.stringify(editorData).length
    });

    // 🎯 调试时长数据来源
    console.log('🕐 时长数据对比:', {
      originalFileDuration: originalFileDuration.value,
      statsVideoDuration: stats.videoDuration,
      statsTotalTime: stats.totalTime,
      finalDuration: editorData.videoFile.duration
    });

    // 🎯 跳转到剪辑页面，使用正确的路由格式
    const projectId = stats.taskId || generateProjectId();
    const filename = selectedFile.value?.name || 'unknown';

    console.log('🚀 跳转到剪辑页面...', { projectId, filename });
    await router.push(`/clip/${projectId}?filename=${encodeURIComponent(filename)}`);

  } catch (error) {
    console.error('❌ 跳转到剪辑页面失败:', error);

    // 🎯 针对 CSP Worker 错误的特殊处理
    if (error.message && error.message.includes('Worker') && error.message.includes('Content Security Policy')) {
      ElMessage({
        type: 'warning',
        message: '页面跳转遇到安全策略限制，请手动刷新页面后重试',
        duration: 5000
      });

      // 尝试直接跳转，不等待页面完全加载
      setTimeout(() => {
        window.location.href = `/clip/${stats.taskId || generateProjectId()}?filename=${encodeURIComponent(selectedFile.value?.name || 'unknown')}`;
      }, 1000);
    } else {
      ElMessage.error('跳转失败: ' + error.message);
    }
  }
};

// WebCodecs 智能处理（参考 index.html 的实现）
const startWebCodecsProcessing = async () => {
  try {
    // 🎯 如果还没有任务，先创建任务
    if (!currentTaskId.value) {
      console.log('📝 开始处理前先创建任务...');
      await createTask();

      if (!currentTaskId.value) {
        throw new Error('任务创建失败');
      }
    }

    // 检查视频格式兼容性
    processingStatus.value = {
      title: '检查视频格式',
      description: '正在分析视频编码格式...'
    };

    await checkVideoCompatibility();

    // 导入必要的模块
    const { VideoProcessingCoordinator } = await import('@/utils/webcodecs-decoder/src/coordinator.js');

    processingStatus.value = {
      title: '初始化协调器...',
      description: '正在设置视频处理协调器'
    };

    // 创建协调器（参考 index.html 的实现）
    const coordinator = new VideoProcessingCoordinator({
      onProgress: (processedFrames: number, totalFrames: number) => {
        // 🎯 使用智能进度计算器
        const stageProgressValue = totalFrames > 0 ? (processedFrames / totalFrames) * 100 : 0;
        const smartCalc = new SmartProgressCalculator();
        smartCalc.setStage('video_processing', stageProgressValue);

        const overallProgressValue = smartCalc.getOverallProgress();

        // 🎯 使用平滑器更新进度，设置更快的速度
        progressSmoother.setSpeed(0.05); // 视频处理阶段更快
        stageProgressSmoother.setTarget(stageProgressValue);
        overallProgressSmoother.setTarget(overallProgressValue);
        progressSmoother.setTarget(overallProgressValue);

        // 更新阶段信息
        stageProgress.value.stage = 'video_processing';
        stageProgress.value.stageName = smartCalc.getStageName();
        stageProgress.value.description = `正在处理视频帧 ${processedFrames}/${totalFrames}`;

        processingStatus.value = {
          title: smartCalc.getStageName(),
          description: `正在处理视频帧 ${processedFrames}/${totalFrames}`
        };
      },
      onComplete: (stats: any) => {
        processingProgress.value = 100;
        stageProgress.value = {
          stage: 'uploading',
          stageName: '处理完成',
          stageProgress: 100,
          overallProgress: 100,
          description: '视频处理和上传已完成'
        };

        processingStatus.value = {
          title: '处理完成',
          description: '智能分段和音频识别已完成'
        };

        // 保存处理结果
        processingResult.value = {
          totalTime: stats.totalTime,
          segmentCount: stats.segmentTimes?.length || 0,
          audioResult: stats.asrResult,
          fusionResult: stats.fusionResult,
          taskId: currentTaskId.value
        };

        // 移除处理警告和页面离开阻止
        showProcessingWarning.value = false;
        isPageUnloadBlocked.value = false;
        window.removeEventListener('beforeunload', handleBeforeUnload);
        isProcessing.value = false;

        // 🎯 处理完成，显示完成界面
        console.log('✅ 视频处理完成，显示完成界面');

        // 设置完成状态
        completionState.value.isCompleted = true;
        completionState.value.resultPreview = stats;

        // 启动自动跳转倒计时
        startAutoJumpCountdown();

        // 更新处理状态
        processingStatus.value = {
          title: '处理完成',
          description: '智能分段和音频识别已完成'
        };
      },
      onError: (error: Error) => {
        // 移除处理警告和页面离开阻止
        showProcessingWarning.value = false;
        isPageUnloadBlocked.value = false;
        window.removeEventListener('beforeunload', handleBeforeUnload);
        throw error;
      },
      onAudioProgress: (status: string, progressPercent?: number) => {
        // 🎯 使用智能进度计算器
        let stageProgressValue = 0;
        if (typeof progressPercent === 'number') {
          stageProgressValue = progressPercent;
        } else {
          // 基于状态文本智能估算进度
          if (status.includes('开始') || status.includes('初始化')) {
            stageProgressValue = 15;
          } else if (status.includes('加载') || status.includes('准备')) {
            stageProgressValue = 30;
          } else if (status.includes('分析') || status.includes('识别')) {
            stageProgressValue = 60;
          } else if (status.includes('处理') || status.includes('转换')) {
            stageProgressValue = 80;
          } else if (status.includes('完成') || status.includes('结束')) {
            stageProgressValue = 95;
          } else {
            stageProgressValue = 45;
          }
        }

        const smartCalc = new SmartProgressCalculator();
        smartCalc.setStage('ai_recognition', stageProgressValue);
        const overallProgressValue = smartCalc.getOverallProgress();

        // 🎯 使用平滑器更新进度，AI阶段使用中等速度
        progressSmoother.setSpeed(0.03);
        stageProgressSmoother.setTarget(stageProgressValue);
        overallProgressSmoother.setTarget(overallProgressValue);
        progressSmoother.setTarget(overallProgressValue);

        // 更新阶段信息
        stageProgress.value.stage = 'ai_recognition';
        stageProgress.value.stageName = smartCalc.getStageName();
        stageProgress.value.description = status;

        processingStatus.value = {
          title: smartCalc.getStageName(),
          description: status
        };
      }
    });

    // 初始化特征提取器（参考 index.html）
    try {
      const extractorType = processingOptions.value.featureExtractor;
      const useBatch = extractorType === 'tensorflow';
      const batchSize = 8;

      await coordinator.initializeFeatureExtractor(extractorType as any, useBatch, batchSize);

      let extractorName = 'Unknown';
      if (extractorType === 'tensorflow') {
        extractorName = 'TensorFlow.js GPU批处理';
      } else if (extractorType === 'tensorflow-single') {
        extractorName = 'TensorFlow.js GPU单帧';
      } else if (extractorType === 'opencv') {
        extractorName = 'OpenCV.js CPU';
      }

      console.log(`🎯 ${extractorName} 特征提取器已启用`);
    } catch (error) {
      console.warn('特征提取启用失败:', error);
    }

    // 设置算法配置（参考 index.html）
    const algorithmConfig = {
      min_avg_duration: 8,
      max_avg_duration: 50,
      initial_pen: 5,
      max_iterations: 20,
      min_size: 10
    };
    coordinator.setAlgorithmConfig(algorithmConfig);
    coordinator.setAlgorithmType(processingOptions.value.algorithmType as any);

    // 设置融合配置
    const fusionConfig = {
      minSegmentDuration: 1.0,
      dialogueProtectionMargin: 0.5
    };
    coordinator.setFusionConfig(fusionConfig);

    // 开始处理
    const targetFps = processingOptions.value.targetFps ? parseInt(processingOptions.value.targetFps) : undefined;
    await coordinator.startProcessing(selectedFile.value!, currentTaskId.value, targetFps);

  } catch (error) {
    console.error('WebCodecs 处理失败:', error);
    throw error;
  }
};

// 基础处理模式（不使用 WebCodecs）
const startBasicProcessing = async () => {
  try {
    // 使用集成工具
    const { createBasicVideoProcessor } = await import('@/utils/webcodecs-integration');

    const processor = createBasicVideoProcessor({
      onProgress: (processedFrames: number, totalFrames: number) => {
        // 保持兼容性的旧版进度回调
        const progress = totalFrames > 0 ? Math.round((processedFrames / totalFrames) * 100) : 0;
        processingProgress.value = progress;
      },
      onStageProgress: (progress: any) => {
        // 三阶段进度更新
        stageProgress.value = {
          stage: progress.stage,
          stageName: progress.stageName,
          stageProgress: progress.stageProgress,
          overallProgress: progress.overallProgress,
          description: progress.description
        };

        // 更新总体进度和状态
        processingProgress.value = progress.overallProgress;
        processingStatus.value = {
          title: progress.stageName,
          description: progress.description
        };
      },
      onComplete: (stats: any) => {
        processingProgress.value = 100;
        processingStatus.value = {
          title: '处理完成',
          description: '基础处理已完成'
        };

        // 保存处理结果
        processingResult.value = {
          totalTime: stats.totalTime,
          segmentCount: stats.segmentTimes?.length || 0,
          audioResult: stats.asrResult,
          taskId: generateProjectId()
        };

        // 移除处理警告和页面离开阻止
        showProcessingWarning.value = false;
        isPageUnloadBlocked.value = false;
        window.removeEventListener('beforeunload', handleBeforeUnload);
        isProcessing.value = false;
      },
      onError: (error: Error) => {
        // 移除处理警告和页面离开阻止
        showProcessingWarning.value = false;
        isPageUnloadBlocked.value = false;
        window.removeEventListener('beforeunload', handleBeforeUnload);
        throw error;
      }
    });

    // 开始处理
    await processor.processVideo(selectedFile.value!, {
      autoSegment: processingOptions.value.autoSegment,
      extractAudio: processingOptions.value.extractAudio,
      generateThumbnails: processingOptions.value.generateThumbnails,
      featureExtractor: processingOptions.value.featureExtractor as any,
      targetFps: processingOptions.value.targetFps,
      algorithmType: processingOptions.value.algorithmType as any
    });

  } catch (error) {
    console.error('基础处理失败:', error);
    throw error;
  }
};

// 查看详细结果
const viewResults = () => {
  if (processingResult.value) {
    // 创建结果展示弹窗或跳转到结果页面
    const resultData = {
      ...processingResult.value,
      fileName: selectedFile.value?.name,
      fileSize: selectedFile.value?.size,
      processingOptions: processingOptions.value
    };

    // 这里可以打开一个模态框显示详细结果
    console.log('详细处理结果:', resultData);
    alert('详细结果已在控制台输出，可以在此基础上实现结果展示界面');
  }
};

// 进入编辑器
const goToEditor = () => {
  if (processingResult.value) {
    const projectId = processingResult.value.taskId || generateProjectId();
    router.push(`/clip/${projectId}?filename=${encodeURIComponent(selectedFile.value!.name)}`);
  }
};

// 生成项目ID
const generateProjectId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// 生命周期
onMounted(async () => {
  // 检查 WebCodecs 支持和服务状态
  await checkWebCodecsSupport();

  // 🎯 不再自动创建任务，等待用户上传文件时再创建

  // 防止页面拖拽文件时的默认行为
  document.addEventListener('dragover', (e) => e.preventDefault());
  document.addEventListener('drop', (e) => e.preventDefault());
});

onUnmounted(() => {
  document.removeEventListener('dragover', (e) => e.preventDefault());
  document.removeEventListener('drop', (e) => e.preventDefault());

  // 清理页面离开事件监听器
  window.removeEventListener('beforeunload', handleBeforeUnload);

  // 🎯 清理进度平滑器动画
  progressSmoother.destroy();
  stageProgressSmoother.destroy();
  overallProgressSmoother.destroy();
});
</script>

<style scoped>
/* 🎯 简约深色主题设计 */
.upload-page {
  min-height: 100vh;
  background: #1a1a1a;
  display: flex;
  flex-direction: column;
  color: #ffffff;
  line-height: 1.6;
}

/* 顶部导航 */
.upload-header {
  padding: 2rem 0;
  text-align: center;
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 🎯 简约标题样式 */
.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 12px;
  letter-spacing: -0.5px;
}

.page-subtitle {
  font-size: 16px;
  color: #888888;
  margin: 0;
  text-align: center;
  font-weight: 400;
}

/* 主要内容 */
.upload-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

/* 🎯 简约上传容器 */
.upload-container {
  background: #262626;
  border: 1px solid #333333;
  border-radius: 12px;
  padding: 48px;
  max-width: 600px;
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-container:hover {
  border-color: #404040;
  transform: translateY(-2px);
}

/* 上传区域 */
/* 🎯 简约上传区域 */
.upload-zone {
  border: 2px dashed #404040;
  border-radius: 8px;
  padding: 80px 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #1f1f1f;
}

.upload-zone:hover {
  border-color: #666666;
  background: #242424;
}

.upload-zone.drag-over {
  border-color: #0066cc;
  background: #001a33;
}

.upload-zone.has-file {
  border-color: #0066cc;
  background: #001a33;
}

.upload-zone.processing {
  border-color: #666666;
  background: #242424;
  cursor: not-allowed;
}

/* WebCodecs 支持警告 */
.support-warning {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.warning-icon {
  flex-shrink: 0;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-weight: 600;
  color: #856404;
  margin-bottom: 0.25rem;
}

.warning-description {
  color: #856404;
  font-size: 0.875rem;
  margin: 0;
}

/* 处理警告提示 */
.processing-warning {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  animation: pulse 2s infinite;
}

.processing-warning .warning-icon {
  flex-shrink: 0;
}

.processing-warning .warning-content {
  flex: 1;
}

.processing-warning .warning-title {
  font-weight: 600;
  color: #92400e;
  margin-bottom: 0.25rem;
}

.processing-warning .warning-description {
  color: #92400e;
  font-size: 0.875rem;
  margin: 0;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 任务管理区域 */
.task-management {
  margin: 1.5rem 0;
  padding: 1rem;
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 8px;
}

.task-title {
  margin: 0 0 1rem 0;
  color: #2d5a2d;
  font-size: 1rem;
  font-weight: 600;
}

.task-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.task-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.task-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.create-task-btn {
  background: #28a745;
  color: white;
}

.create-task-btn:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-1px);
}

.delete-task-btn {
  background: #dc3545;
  color: white;
}

.delete-task-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.task-info {
  flex: 1;
  min-width: 200px;
  color: #666;
  font-size: 0.875rem;
}

/* 上传提示 */
.upload-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  margin-bottom: 1.5rem;
}

/* 🎯 简约标题和描述 */
.upload-title {
  font-size: 18px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 8px;
}

.upload-description {
  color: #888888;
  margin-bottom: 24px;
  font-size: 14px;
}

.upload-formats {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.format-tag {
  background: #e5e7eb;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* 文件选中状态 */
.file-selected {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.file-icon {
  flex-shrink: 0;
}

.file-info {
  flex: 1;
  text-align: left;
}

.file-name {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.file-size {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.remove-file-btn {
  flex-shrink: 0;
  padding: 0.5rem;
  border: none;
  background: #fee2e2;
  color: #dc2626;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-file-btn:hover {
  background: #fecaca;
}

/* 处理中状态 */
.processing-state {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.processing-spinner {
  margin-bottom: 1rem;
}

.processing-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.processing-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
  text-align: center;
}

/* 三阶段进度显示 */
.stage-info {
  width: 100%;
  max-width: 400px;
  margin-bottom: 1.5rem;
}

.stage-indicators {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
  gap: 0;
}

.stage-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.stage-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e5e7eb;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stage-indicator.active .stage-dot {
  background: #3b82f6;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

.stage-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
}

.stage-indicator.active .stage-label {
  color: #3b82f6;
  font-weight: 600;
}

.stage-connector {
  width: 60px;
  height: 2px;
  background: #e5e7eb;
  margin: 0 1rem;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem; /* 对齐到 stage-dot 的位置 */
}

.stage-connector.active {
  background: #3b82f6;
}

.processing-progress {
  width: 100%;
  max-width: 400px;
  margin-bottom: 1rem;
}

.stage-progress {
  width: 100%;
  max-width: 400px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.progress-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  border-radius: 4px;
  transition: width 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;

  /* 🎯 添加脉冲效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    border-radius: inherit;
    opacity: 0.6;
    animation: pulse 2s ease-in-out infinite;
  }
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.stage-progress-bar {
  height: 6px;
}

.stage-progress-fill {
  background: linear-gradient(90deg, #10b981, #059669);
  transition: width 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* 🎯 阶段进度条的流光效果 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    animation: shimmer 1.5s infinite;
  }
}

/* 🎯 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    transform: scaleY(1);
    opacity: 0.6;
  }
  50% {
    transform: scaleY(1.1);
    opacity: 0.8;
  }
}

.progress-text {
  font-weight: 600;
  color: #374151;
  min-width: 40px;
  font-size: 0.875rem;
}

/* 处理选项 */
.processing-options {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.options-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1.5rem;
}

.option-group {
  margin-bottom: 1rem;
}

.option-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.option-label:hover {
  background: #f9fafb;
}

.option-checkbox {
  margin-top: 0.125rem;
  width: 1rem;
  height: 1rem;
  accent-color: #3b82f6;
}

.option-text {
  font-weight: 500;
  color: #374151;
  display: block;
  margin-bottom: 0.25rem;
}

.option-description {
  font-size: 0.875rem;
  color: #6b7280;
  display: block;
}

/* 选项区域 */
.option-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.option-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

/* 内联选项 */
.option-row {
  margin-bottom: 0.75rem;
}

.option-label-inline {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.option-label-inline:hover {
  background: #f9fafb;
}

.option-select {
  padding: 0.375rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  min-width: 160px;
}

.option-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 操作按钮 */
.upload-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.cancel-btn {
  flex: 1;
  padding: 0.75rem 1.5rem;
  border: 2px solid #e5e7eb;
  background: white;
  color: #374151;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

/* 🎯 简约按钮样式 */
.upload-btn {
  flex: 2;
  padding: 12px 24px;
  border: none;
  background: #0066cc;
  color: #ffffff;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn:hover {
  background: #0052a3;
  transform: translateY(-1px);
}

.upload-btn:disabled {
  background: #333333;
  color: #666666;
  cursor: not-allowed;
  transform: none;
}

/* 处理结果展示 */
.processing-result {
  margin-top: 2rem;
  padding: 2rem;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 12px;
}

.result-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #166534;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.result-title::before {
  content: '✅';
  font-size: 1.5rem;
}

.result-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.result-label {
  font-weight: 500;
  color: #374151;
}

.result-value {
  font-weight: 600;
  color: #059669;
}

.result-actions {
  display: flex;
  gap: 1rem;
}

.view-results-btn, .go-editor-btn {
  flex: 1;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-results-btn {
  background: #e5e7eb;
  color: #374151;
}

.view-results-btn:hover {
  background: #d1d5db;
}

.go-editor-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.go-editor-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-container {
    margin: 1rem;
    padding: 2rem 1.5rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .upload-zone {
    padding: 2rem 1rem;
  }

  .upload-actions {
    flex-direction: column;
  }
}

/* 🎯 完成界面样式 */
.completion-container {
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  border: 1px solid #bbf7d0;
  border-radius: 16px;
  padding: 2rem;
  margin-top: 1.5rem;
}

.completion-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.completion-icon {
  width: 48px;
  height: 48px;
  flex-shrink: 0;
}

.completion-icon svg {
  width: 100%;
  height: 100%;
}

.completion-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #166534;
  margin: 0 0 0.25rem 0;
}

.completion-description {
  color: #15803d;
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

.result-preview {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #d1fae5;
}

.preview-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat-item {
  text-align: center;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.completion-actions {
  margin-top: 1.5rem;
}

.choice-title {
  font-size: 1rem;
  font-weight: 500;
  color: #374151;
  margin: 0 0 1rem 0;
}

.choice-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.choice-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.choice-btn:hover {
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.choice-btn.active {
  border-color: #4f46e5;
  background: #f8faff;
}

.choice-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.choice-content {
  text-align: left;
}

.choice-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.choice-desc {
  font-size: 0.75rem;
  color: #6b7280;
}

.auto-jump-notice {
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.countdown-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.countdown-text {
  font-size: 0.875rem;
  color: #92400e;
  font-weight: 500;
}

.cancel-btn {
  background: none;
  border: 1px solid #d97706;
  color: #d97706;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background: #d97706;
  color: white;
}

.countdown-progress {
  height: 4px;
  background: #fed7aa;
  border-radius: 2px;
  overflow: hidden;
}

.countdown-fill {
  height: 100%;
  background: #d97706;
  transition: width 1s linear;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn-outline {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-outline svg {
  width: 16px;
  height: 16px;
}

.btn-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.btn-primary svg {
  width: 16px;
  height: 16px;
}

/* 🎯 响应式优化 */
@media (max-width: 768px) {
  .completion-container {
    padding: 1.5rem;
    margin: 1rem;
  }

  .choice-buttons {
    grid-template-columns: 1fr;
  }

  .preview-stats {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>
